/* Base body and app layout */
body {
    font-family: sans-serif;
    display: flex;
    height: 100vh;
    margin: 0;
    color: var(--primary-text);
    background-color: var(--primary-bg);
    transition: background-color 0.3s, color 0.3s;
}

#app {
    display: flex;
    width: 100%;
    height: 100%;
}

/* Sidebar Styles */
#sidebar {
    width: 280px;
    background-color: var(--secondary-bg);
    border-right: 1px solid var(--border-color);
    padding: 1rem;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    flex-shrink: 0; /* Prevent sidebar from shrinking */
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-header h2 {
    margin: 0;
    font-size: 1.2em;
    color: var(--secondary-text);
}

#theme-toggle {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    background-color: var(--button-bg);
    color: var(--text-on-accent);
    cursor: pointer;
    border-radius: 5px;
    font-size: 0.8em;
}

#theme-toggle:hover {
    background-color: var(--button-hover-bg);
}

#agents-list, #groups-list {
    margin-bottom: 1rem;
}

h3 {
    color: var(--secondary-text);
    margin-top: 0;
}

.sidebar-item {
    display: flex;
    align-items: center;
    padding: 8px;
    cursor: pointer;
    border-radius: 4px;
    margin-bottom: 4px;
    transition: background-color 0.2s;
}

.sidebar-item:hover {
    background-color: var(--accent-bg);
}

.sidebar-item .avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 10px;
    object-fit: cover;
}

/* Main Content Area */
#main-content {
    flex-grow: 1;
    padding: 1rem;
    overflow-y: auto;
    background-color: var(--tertiary-bg);
    display: flex;
    flex-direction: column;
}

#tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1rem;
    flex-shrink: 0;
}

.tab-button {
    padding: 10px 15px;
    cursor: pointer;
    border: none;
    background-color: transparent;
    color: var(--secondary-text);
    border-bottom: 2px solid transparent;
    font-size: 1em;
}

.tab-button.active {
    color: var(--highlight-text);
    border-bottom-color: var(--highlight-text);
}

#tab-content {
    flex-grow: 1;
    overflow-y: auto;
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

/* Topics List */
.topics-list {
    list-style: none;
    padding: 0;
}

.topics-list li {
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid var(--border-color);
}

.topics-list li:hover {
    background-color: var(--accent-bg);
}

/* Chat History Messages */
.message {
    padding: 12px;
    margin-bottom: 10px;
    border-radius: 8px;
}

.message.role-user {
    background-color: var(--user-bubble-bg);
}

.message.role-assistant {
    background-color: var(--assistant-bubble-bg);
}

.message-header {
    font-size: 0.8em;
    color: var(--secondary-text);
    margin-bottom: 5px;
}

.message-header .name {
    font-weight: bold;
    color: var(--highlight-text);
}

.message-header .timestamp {
    float: right;
}

.content {
    white-space: pre-wrap; /* This is the key to preserving whitespace and newlines */
    word-wrap: break-word;
    cursor: pointer; /* Indicate that the content is clickable */
}

.edit-textarea {
    width: 100%;
    box-sizing: border-box;
    border: 1px solid var(--highlight-text);
    border-radius: 4px;
    padding: 8px;
    font-family: inherit;
    font-size: inherit;
    color: var(--primary-text);
    background-color: var(--input-bg);
    resize: vertical;
    min-height: 50px;
}

/* Modern Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-thumb, #888);
    border-radius: 10px;
    border: 2px solid transparent;
    background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
    background-color: var(--scrollbar-thumb-hover, #555);
}

/* Resizer Handle */
#resizer {
    width: 5px;
    cursor: col-resize;
    background-color: var(--border-color);
    transition: background-color 0.2s;
}

#resizer:hover {
    background-color: var(--highlight-text);
}

.content img {
    max-width: 100%;
    border-radius: 4px;
    margin-top: 5px;
}

/* JSON Editor */
#json-editor pre {
    background-color: var(--secondary-bg);
    border: 1px solid var(--border-color);
    color: var(--primary-text);
    padding: 1rem;
    border-radius: 5px;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: monospace;
}

/* Attachment Viewer */
.attachment-category-title {
    width: 100%;
    padding-bottom: 8px;
    margin-top: 20px;
    margin-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
    color: var(--highlight-text);
    cursor: pointer;
    position: relative;
    user-select: none; /* Prevent text selection on click */
}

.attachment-category-title.collapsible::before {
    content: '▼'; /* Expanded state */
    position: absolute;
    left: -15px;
    transition: transform 0.2s;
}

.attachment-category-title.collapsible.collapsed::before {
    transform: rotate(-90deg); /* Collapsed state */
}

.attachment-grid.collapsed {
    display: none;
}

.attachment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 20px;
}

.attachment-item {
    background-color: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: 5px;
    padding: 10px;
    text-align: center;
    cursor: pointer;
    transition: background-color 0.2s;
}

.attachment-item:hover {
    background-color: var(--accent-bg);
}

.attachment-item img {
    max-width: 100%;
    height: 80px;
    object-fit: cover;
    margin-bottom: 10px;
}

.attachment-item .filename {
    font-size: 0.8em;
    word-wrap: break-word;
}
/* Search Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--secondary-bg);
    padding: 20px;
    border-radius: 8px;
    width: 80%;
    max-width: 700px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.modal-header h3 {
    margin: 0;
}

.close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--primary-text);
}

.modal-body {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

#search-input {
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    border-radius: 5px;
    border: 1px solid var(--border-color);
    background-color: var(--input-bg);
    color: var(--primary-text);
    font-size: 1rem;
    box-sizing: border-box;
}

#search-results {
    flex-grow: 1;
    overflow-y: auto;
}

.search-result-item {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.2s;
}

.search-result-item:hover {
    background-color: var(--accent-bg);
}

.search-result-item .context {
    font-size: 0.8em;
    color: var(--secondary-text);
    margin-bottom: 5px;
}

.search-result-item .content {
    font-size: 0.95em;
}

.search-result-item .content strong {
    color: var(--highlight-text);
    background-color: var(--highlight-bg);
}

#search-pagination {
    padding-top: 10px;
    margin-top: 10px;
    border-top: 1px solid var(--border-color);
    text-align: center;
}

.pagination-button {
    padding: 8px 16px;
    margin: 0 5px;
    border: 1px solid var(--border-color);
    background-color: var(--button-bg);
    color: var(--text-on-accent);
    cursor: pointer;
    border-radius: 5px;
}

.pagination-button:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

.message.message-highlight {
    box-shadow: 0 0 10px 3px var(--highlight-text);
    transition: box-shadow 0.5s ease-in-out;
}