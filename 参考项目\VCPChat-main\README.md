# VCP 聊天客户端

这是一个为 VCP (Variable & Command Protocol) 服务器打造的 AI 聊天桌面客户端。

*请立刻在全局设置里设置用户名，以避免众多依赖功能报错！*

后端链接：https://github.com/lioensky/VCPToolBox

请下载壁纸包以避免元素窗口异常：https://github.com/lioensky/VCPChat/releases

## 前后端深度协同：释放 AI 的无限潜能

VChat 不仅仅是一个聊天界面，它是强大的 VCP 后端生态（VCPToolBox）的“眼睛”和“画板”。两者深度结合，旨在释放 AI 的无限潜能：

*   **高级能力渲染**: VChat 专为渲染 VCP 协议的复杂输出而设计。无论是 AI 主动记录和反思的“日记”，还是多媒体内容，都能以最直观的方式呈现。Vchat的Agent输出气泡拥有近乎怪物级的渲染能力，可以渲染几乎所有主流动画和文档。
*   **重型异步任务**: 后端可以执行如视频生成、数据分析等耗时很长的重型任务。AI 发起任务后可立即响应用户，任务完成后，VChat 会通过后端推送，将结果（如生成的视频）实时展现在对话流中，整个过程流畅不卡顿。
*   **Agent 群体智能**: 后端支持多个 AI Agent 协同工作，甚至实现 Agent 主动分配任务给子 Agent。VChat 的群聊模式和清晰的发言标记系统，为这种高级的“AI 女仆团”协作流提供了完美的交互界面。
*   **丰富的多媒体交互**: AI 可以通过后端调用 VCP 工具，在对话中发送表情包、播放音乐、展示视频。VChat 拥有强大的多媒体渲染引擎和高级的窗口气泡动画、流式动画，确保了极致的视听体验。
*   **VCP 核心多模态能力**: 这使得例如 FluxGen 或 SunoGen 这样的插件生成的多媒体，AI 真的可以看到和听到。
    *   **Base64 直通车**: 允许 AI 在 `tool` 字段中直接引入 Base64 数据，极大地简化了多媒体内容的即时调用。
    *   **全局文件 API (`VCPFileAPI`)**: 提供统一的接口，用于处理和中转各类多媒体文件，是多模态数据流转的核心枢纽。
    *   **跨模态智能转译**: 实现高阶模型对低阶模型的“能力赋能”。例如，能识别音频的模型可以帮助纯文本模型，将其无法处理的音频 Base64 数据智能转译为文字描述，反哺给请求方。
    *   **分布式多模态传输**: 在 VCP 分布式节点之间建立起高效的多模态数据传输链，确保 Base64 等大数据块能够在不同服务器间可靠传递。
    *   **智能响应路由**: VCP 核心能够智能判断插件返回的是传统的 stdio 文本信息，还是包含 Base64 的结构化数据，并自动选择正确的渠道进行转发和处理。
    *   **多 Agent 协作共享**: 在多 Agent 协同任务中，实现 Base64 数据的智能共享，并能根据需要将其动态转译为临时的 `fileurl`，方便其他 Agent 或前端应用访问。

## 主要功能

*   **VCP 服务器集成**: 客户端的核心聊天功能依赖于 VCP 服务器。它通过 HTTP(S) 与 VCP 服务器通信，发送用户消息并接收 AI 的响应，支持流式传输以实现实时交互。
*   **VCP 工具调用**: 完美支持 VCP 服务器定义的各类工具调用，包括需要立即返回结果的**同步工具**（如计算、查询）和可后台执行的**异步工具**（如视频生成、网站长文抓取），让 AI 的能力边界无限扩展。
*   **VCP 日记渲染**: 能够渲染和显示 VCP 日记内容。这不仅是查看日志，更是观察 AI 如何形成长期记忆、实现自我进化的窗口。
*   **Agent 管理**:
    *   创建、删除和配置多个 AI Agent。
    *   为每个 Agent 设置名称、系统提示、模型参数（如温度、上下文Token限制、最大输出Token）。
    *   管理 Agent 的头像。
    *   支持每个 Agent 拥有多个独立的聊天话题 (Topics)，包括话题的创建、删除、重命名和排序。
    *   支持 Agent 列表的自定义排序。
*   **高级上下文管理 (兼容 SillyTavern)**: VChat 基于后端服务器节点实现了与 SillyTavern 高度兼容的上下文管理机制，为精细化、可复用的对话背景设定提供了强大支持。
    *  **预设、角色卡与世界书**: 现在VCP系统完全兼容并支持挂载 SillyTavern 的 `预设 (Preset) `、 `角色卡 (Character Card) ` 和  `世界书 (World Book)`。您可以无缝导入和使用已有的 SillyTavern 资源，或在 VCP 内直接创建和管理。
    *  **可视化预设编辑器**: 内置强大的可视化编辑器，允许您创建和编辑上下文预设。支持设置复杂的注入规则，如 `深度注入 (Deep Injection) ` 和  `相对注入 (Relative Injection) `，精确控制每一条上下文在对话历史中的位置和行为。
    *  **拖拽式上下文排序**: 在聊天界面中，所有注入的上下文（如系统提示、角色设定、世界信息等）都清晰可见，并支持通过 `拖拽 `方式实时调整它们的相对顺序，直观地改变 AI 的行为优先级。
    *  **Agent 独立挂载**: 每个 Agent 都可以独立挂载不同的预设和世界书组合。这意味着您可以为“写作助手”Agent 配置一套专业的写作背景资料，同时为“聊天伴侣”Agent 设置另一套完全不同的角色扮演设定，实现高度个性化的 AI 体验。
*   **群聊模式 (Agent Groups)**:
    *   允许多个已配置的 Agent 在同一个聊天会话中进行协作或角色扮演。
    *   支持创建、配置和管理 Agent 群组，包括设置群组名称、头像。
    *   每个群组可以包含多个从现有 Agent 列表中选择的成员。
    *   **发言模式**:
        *   **顺序发言 (`sequential`)**: 成员按预定顺序轮流发言（当前实现为按成员列表顺序，每次一个，具体高级轮换逻辑可后续增强）。
        *   **自然随机 (`naturerandom`)**: 根据用户输入中的 `@角色名`、`@角色标签` 或消息内容中与成员预设标签匹配每个Agent的关键词/描述词，来智能产生上下文权重决定哪些成员响应。此模式在构建自然权重回复序列时还保留有一定的随机性，并可能在没有明确触发时选择一个保底发言者。
        *   **邀约模式 (`inviteonly`)**:根据用户点击Agent的按钮来决定谁来发言。
    *   **群组设定 (`groupPrompt`)**: 可以为整个群聊定义一个共同的背景、规则或系统级指令，影响群内所有 Agent 的行为。
    *   **发言邀请 (`invitePrompt`)**:
        *   这是一个模板字符串，用于在群聊中由系统（或协调者Agent）提示特定 Agent 发言。
        *   模板中应使用 `{{VCPChatAgentName}}` 作为占位符，系统在实际邀请时会自动将其替换为目标 Agent 的名称。
        *   **默认 `invitePrompt` 示例**：`现在轮到你{{VCPChatAgentName}}发言了。系统已经为大家添加[xxx的发言：]这样的标记头，以用于区分不同发言来自谁。大家不用自己再输出自己的发言标记头，讨论时不要讨论这个标记头系统，专注正常聊天即可。`
        *   这个提示旨在引导 Agent 自然地开始其回合，同时告知它们关于发言标记的规则。
    *   **发言标记系统**:
        *   为了在包含多个 Agent 和用户的群聊中清晰地标识每一条消息的来源，系统会自动在每条消息（无论是用户还是 Agent 的）前添加发言者标记，格式通常为 `[发言者名称的发言]: 实际消息内容`。
        *   **重要提示**：用户和配置的 Agent 在聊天时**无需手动输入或模仿**这些标记头。Agent 的系统提示和 `invitePrompt` 也应引导它们专注于对话内容，而不是讨论或生成这些标记。
    *   群组同样支持独立的话题管理，包括话题的创建、删除、重命名和排序。
*   **跨端记忆**:
    *   基于VCP后端实现了Agent永久记忆/跨端记忆/时间轴记忆。
    *   AI拥有完整的跨话题唯一标识化认知，并会对所有工具调用不断反思、优化与学习。
*   **聊天界面**:
    *   提供用户友好的聊天界面进行 AI 交互。
    *   支持 Markdown/Katex/Html/Mermaid/VCPTool…等21种渲染器 渲染聊天消息，包括代码块高亮。
    *   **强大的多媒体与文件处理**:
        *   支持通过文件选择器、粘贴（文件路径或多媒体数据）、拖放操作添加附件。
        *   能够从剪贴板读取和粘贴多媒体(兼容几乎市面上所有多媒体文件和文档文件)，并直接在聊天中发送。
        *   支持将过长的文本粘贴内容自动保存为文本文件附件。
        *   内置高级图片查看器，方便在独立窗口预览聊天中的图片进行操作，支持复制和外部打开。
        *   **@附加笔记**: 在输入框中输入 `@` 符号并跟上关键词，即可快速搜索并附加 `AppData/Notemodules` 目录下的笔记文件，实现知识的无缝调用。
    *   **高级动态渲染**: VChat 不仅能渲染静态文本，更能无缝展示由后端 AI 生成或调用的丰富多媒体内容，如**音乐、视频、动态表情包、交互文档**等，为 VCP 协议的强大能力提供一个表现力丰富的舞台。
    *   **DIV 元素流式渲染**: 针对 AI 输出的复杂 DIV 气泡主题内容，VChat 实现了创新的流式渲染机制。它不仅仅是简单地展示内容，而是完美兼容 VChat 内置的21种渲染器（如Markdown、Python、Mermaid等）的流式实现，并新增了对Anime.js的流式渲染兼容，实现了和md渲染器的优雅竟态处理。VChat优雅地处理了各种极端复杂的渲染竞态问题，例如：当DIV中包裹着需要实时执行的Python代码块（气泡会动态渲染出代码的运行结果），当Python代码中又包含了需要加载的`src`图片标签，当表格中需要内嵌一篇完整的Markdown文档，或者当表格单元格里需要显示一张图片时，VChat的渲染引擎都能智能地、按正确的依赖顺序进行渲染，确保这些复杂的、互相嵌套的内容被准确、流畅地拼接成一个完整的动态气泡，提供了业界领先的复杂内容呈现能力。
    *   **高级气泡主题**: Vchat 允许每一个主题文件独立设计聊天气泡样式和动画，允许Agent为自己的每一个输出气泡设置独立的气泡样式和内部动画，允许气泡交互性元素，支持agent对自身气泡的div/js/canvas自定义，使得Agent可以输出完整2D/3D的气泡元素内容。
    *   **高级阅读模式**: 对 AI 发送的长内容提供功能丰富的沉浸式阅读体验。
        *   **多格式渲染**:
            *   支持 **Markdown** 全功能渲染，包括表格、列表、引用等。
            *   支持 **LaTeX** 数学公式渲染 (KaTeX)，完美显示复杂公式。
            *   支持 **Mermaid** 图表渲染，可将代码块直接转换为流程图、序列图等。
            *   实现了对Anime.js的渲染兼容，HTML播放中也实现了对Anime.js渲染的兼容。
        *   **交互式代码块**:
            *   所有代码块均支持语法高亮 (Highlight.js)、一键复制和**块内编辑**。
            *   **HTML 渲染**: `html` 代码块右上角提供“预览”按钮，可直接在应用内渲染和查看 HTML 效果。
            *   **Python 执行**: `python` 代码块右上角提供“运行”按钮，利用 **Pyodide (WASM)** 技术直接在客户端执行 Python 代码并显示输出，非常适合代码演示和数据操作。
            *   **Python 穿透执行** `python` 代码块在信任模式下直接调用win底层库来直接执行，允许直接操作系统内容，基于系统底层编译环境。
            *   **Three.js 3D 预览**: `javascript` 或 `js` 代码块若包含 `three.js` 代码，将提供“预览”按钮，可在沙箱环境中实时渲染和交互 3D 动画。
            *   **自动代码补全**：对代码格式拥有一定的自动补全功能。
        *   **便捷的全局操作**:
            *   支持对整个阅读内容进行**一键编辑**或快速**分享到笔记**模块。
            *   提供强大的自定义上下文菜单（复制、剪切、删除、编辑全文、复制全文）。
            *   **分享截图**: 在右键菜单中提供“分享截图”功能，可将当前渲染精美的DIV卡片（如AI日报）完整截取为一张图片，并在图片查看器中预览，方便用户分享到社交媒体或进行保存。
    *   聊天分支功能，可以基于现有对话创建新的聊天分支。
    *   **收藏到笔记**: Agent气泡右键提供“收藏”按钮，可一键将当前消息内容（包括复杂的渲染格式）完整保存到指定的笔记文件中，方便知识沉淀和后续查阅。

## AI表情包URL修复器

VChat 现在内置了一个强大且智能的AI表情包URL修复器，旨在解决AI在发送表情包时可能出现的各种URL错误问题。

### 功能简介

AI在生成表情包的`<img>`标签时，有时会因为模型幻觉或数据偏差，导致URL中的IP地址、端口、密码、表情包分类目录或文件名出现错误。此功能可以：
*   **自动检测**: 智能识别消息中指向表情包的图片链接。
*   **模糊匹配**: 当检测到URL无法访问(404)时，它会利用内置的表情包“知识库”，通过模糊匹配算法，从错误的URL中提取关键信息（如文件名），并找到最相似的正确表情包。
*   **无缝修复**: 如果找到一个高置信度的匹配项，它会自动替换为正确的URL进行渲染，整个过程对用户透明。
*   **智能放行**: 如果URL完全正确，或者错误得离谱以至于无法匹配任何已知表情包，修复器会放弃修复，按原样渲染，避免错误干预。

### 如何配置

要启用此功能，您需要从后端的 VCPToolbox 项目中同步表情包列表缓存到VChat客户端。

1.  **复制表情包列表**:
    *   找到您的 VCPToolbox 后端项目。
    *   将其中的 `Vcptoolbox/plugin/EmojiListGenerator/generated_lists` 整个文件夹复制。
    *   粘贴到 VChat 项目的 `AppData/` 目录下。最终路径应为 `VChat/AppData/generated_lists`。

2.  **配置图床密码**:
    *   在刚刚复制的 `VChat/AppData/generated_lists/` 文件夹内，手动创建一个名为 `config.env` 的文本文件。
    *   打开 `config.env` 文件，并写入您的图床密码，格式如下：
        ```
        file_key=你的图床密码
        ```
        例如: `file_key=123456`

完成以上步骤后，重启VChat客户端，修复器即可自动开始工作。    


*   **聊天历史/用户数据管理器 (VchatManager)**:
    *   新增一个独立的、基于 Electron 的可视化管理工具 (`VchatManager/`)，用于查看和编辑 `AppData` 中的用户数据。
    *   **聊天记录查看与编辑**: 直观地浏览所有 Agent 和群组的聊天历史，并支持直接在界面中编辑消息内容。
    *   **JSON 编辑器**: 提供原始 JSON 格式的聊天记录视图，方便高级用户进行检查和修改。
    *   **附件浏览器**: 集中展示 `UserData/attachments` 目录下的所有附件，并按图片、音频、视频等类型进行分类，展示其与聊天历史文件中的FileAPI字段绑定依赖关系。
    *   **全局聊天记录搜索**: 内置强大的全局搜索功能 (`Ctrl+F`)，可以快速在所有历史记录中查找特定内容，并直接定位到对应的消息气泡。
*   **主程序全局聊天搜索**:
    *   现在，主聊天程序同样内置了强大的全局搜索功能 (`Ctrl+F`)。
    *   可以快速、高效地在所有 Agent 和群组的所有话题中搜索聊天内容。
    *   搜索结果会以列表形式清晰展示，并支持翻页。
    *   点击任意搜索结果，即可自动跳转到对应的聊天窗口和话题，并高亮显示该条消息，实现了无缝的上下文追溯。
*   **翻译模块**:
    *   独立的翻译窗口，方便快速翻译。
    *   支持全语种互翻。
    *   支持通过自然语言定义翻译格式，例如Latex布局，CSV布局，MD布局等等。
*   **笔记模块**:
    *   独立的笔记管理窗口，方便记录和整理信息。
    *   支持创建、读取、更新和删除 TXT/MD/Latex/RTF/pdf 格式的笔记。
    *   支持创建笔记文件夹管理。
    *   笔记编辑器支持 Markdown 语法，并提供实时预览功能（包括代码高亮和 LaTeX）。
    *   支持在笔记中粘贴图片，图片将作为附件保存并自动插入 Markdown 链接。
    *   笔记支持Html/Latex/Mermaid/CSV渲染。
    *   提供笔记搜索功能，快速定位所需内容。
    *   自动保存机制，防止笔记内容丢失。
    *   支持从聊天消息或其他应用内容“分享到笔记”，快速创建新笔记。
    *   支持分享笔记到AI知识库。
    *   可在聊天窗口@任意笔记库的笔记发送给AI。
    *   **深度知识库集成**: 允许直接编辑和管理 Agent 的核心知识库与长期记忆。
    *   **云端同步**: 支持接入并同步云端笔记数据库（如 Obsidian 类）。
 *   **数据存储**:
    *   聊天记录、Agent 配置、笔记内容和附件等数据安全地存储在项目内的 `AppData` 目录中。
    *   支持基于VCP后端的数据同步。
 *   **VCPLog 集成**:
    *   通过 WebSocket 连接到 VCPLog 服务，实时接收和显示来自 VCP 服务器的日志信息，方便调试和监控。
    *   支持完整的VCP通知与VCP异步任务回调。
    *   支持重要信息的邮件/Win系统级通知广播。
 *   **自定义设置**:
    *   允许用户配置应用程序的一些基本设置，如用户名、VCP 服务器地址、VCPLog 服务地址等。
    *   服务器地址为 `http://yourip:6005/v1/chat/completions`，通知地址通常为 `ws://yourip:6005`。Https则对应wss。
 *   **窗口与交互**:
    *   自定义窗口框架和控制按钮（最小化、最大化/还原、关闭）。
    *   提供全局快捷键，例如 `Control+Shift+I` 快速打开开发者工具。
    *   打开外部链接前进行安全检查，提升安全性。
 *   **划词小助手 (Selection Assistant)**:
    *   **全局文本监听**: 在设置中启用后，可在任何应用程序中通过鼠标划选文本来激活。
    *   **悬浮动作条**: 划选文本后，会在鼠标附近出现一个悬浮工具条，提供快捷操作按钮（如翻译、总结、解释、搜索、配图等）。
    *   **调用内部 Agent**: 所有快捷操作都会调用在设置中预先指定的 VCP Agent 来执行，充分复用现有 AI 能力。
    *   **独立对话窗口**: 点击快捷操作后，会弹出一个独立的、轻量的聊天窗口，显示该 Agent 对划选文本的处理过程和结果。
    *   **无缝体验**: 整个过程无需离开当前工作窗口，实现了高效的即时信息处理。
    *   **分享笔记**：允许将任意圈选内容或窗口分享到笔记。
 *   **VCP 分布式服务器兼容**:
    *   内置一个与 VCP 后端兼容的分布式服务器功能。
    *   启用后，允许 VCP 后端调用并利用此客户端（前端设备）的算力来执行任务。
    *   支持 VCP 后端调用在客户端本地加载的 VCP 插件，扩展了工具执行的能力。
    *   此功能可在“全局设置”中方便地开启或关闭。
 *   **音乐播放器与控制**:
    *   内置一个功能完善的音乐播放器，支持播放本地音乐文件，支持WASPI光纤同轴输出。
    *   提供播放、暂停、上一曲、下一曲、音量控制等基本功能。
    *   **Agent 音乐控制**: 允许 AI Agent 通过 VCP 服务器调用客户端的音乐控制功能，实现 AI 驱动的音乐播放和管理。此功能可在“全局设置”中开启或关闭。
    *   **音乐互动**: Agent 不仅能控制播放，还能“听懂”音乐内容（现在音乐播放器播放的音乐会实时的被agent听到），或通过歌词文件进行翻唱，与用户进行卡拉OK互动。在聊天过程中，Agent还会主动为你点歌活跃氛围。
 *   **文件系统集成**:
    *   **文件管理器**: Agent 能够读取本地任意路径下的文件，包括纯文本、富文本文档（如 PDF、Office 文件）、扫描件以及常见多媒体文件（图片、音频、视频等），并在用户设定的信任目录下拥有完整的读写权限，支持批量创建、编辑和调试文件。
    *   **多媒体操作**: Agent 能够对多媒体文件进行处理，例如从视频里提取图片、提取音频，以及对音频进行切割分段。
    *   **全局文件搜索**: 集成强大的全局快速搜索功能（基于 EverythingAPI魔改），允许 Agent 在整个电脑范围内查找文件，允许检索图片，音频内部多媒体内容。
 *   **深度回忆功能**:
    *   允许 Agent 根据关键词和请求的上下文窗口大小，检索其过去所有完整聊天历史记录，实现精准、深入的长期记忆回顾。
 *   **强大的主题系统**:
    *   **主题选择器**: 主界面新增主题选择器，可以实时预览 VChat 的多主题渲染效果和布局动画，方便用户进行个性化选择和配置。
    *   **主题生成器**: 用户可通过自然语言与专门的“主题管理 Agent”对话，直接生成 VChat 主题。支持用户上传壁纸或素材，由 Agent 辅助创建包含复杂 UI 和动画的自定义主题文件。
 *   **超级浏览器控制 (需要 VCP 浏览器遥控器插件)**: 这并非简单的远程控制，而是一项革命性的技术。该引擎能将**任意**浏览器标签页实时“翻译”成一个动态的、可交互的 Markdown 文档，并建立了一种 Agent 与用户协同浏览的全新交互模式，让 AI 真正成为您的智能上网伴侣。
     *   **实时感知与理解**: 具备智能刷新机制，能实时将页面上的可交互元素、图片、视频、脚本等内容以自然语言形式同步给 Agent，让 AI 能“看懂”和“理解”网页。
     *   **网页截图**: 允许AI获取网页截图，用于视觉分析或存档。
     *   **Base64数据抓取**: 允许AI抓取网页多媒体的Base64数据进行阅览。
     *   **反向精确操控**: AI 只需使用简单的 Markdown 语法，如 `![搜索:XXX]` 或 `[点击按钮: 登录]`，即可精确操控页面上的任意元素。
*   **Vchat超级骰子插件**:
    *   **真实3d物理骰子**: 允许用户或者Agent丢出任意骰子组合，支持"d4","d6","d8","d10","d12","d20","d100"。
    *   **骰子主题**: 允许用户或者Agent定义自己的骰子的材质包和主题色，目前预置十多种骰子主题。
    *   **同步返回结果**: Agent 可以真实观看自己丢出的骰子的结果和主题效果。
    *   **物理魔法**: 允许Agent对骰子进行“物理施法”，例如打滑骰子，黏着骰子，磁铁骰子等等3D附加物理效果。
*   **V日报插件**: 这不仅是一个新闻聚合器，更是一个全自动的AI新闻编辑部。它将每日全球资讯转化为一份精美的、可交互的个性化日报，直接送达您的聊天窗口。
    *   **工作流程**:
        1.  **全球信息扫描**: 首先，Agent会启动一个强大的信息雷达，扫描全球超过100个主流门户网站，从科技、财经到文化、生活等20个不同领域捕获最新的新闻热点，形成一个包含超过2000条新闻线索的庞大初始信息池。
        2.  **AI编辑筛选与深度挖掘**: 接着，Agent会像一位资深编辑，根据预设或用户的兴趣，从信息池中筛选出最具价值的议题。然后，它会启动VChat内置的“超级爬虫”，对选定的新闻进行深度内容抓取，获取全文、关键图片和相关数据。
        3.  **期刊级排版与生成**: 最后，Agent会将处理好的素材进行智能排版，自动划分版块、配置图片、撰写摘要，最终生成一份媲美专业网页期刊的、高度动态化的DIV气泡。
    *   **用户体验**: 您收到的不是一堆链接，而是一份结构完整、图文并茂的动态报纸。您可以在其中自由滚动、点击交互，享受沉浸式的新闻阅读体验，仿佛拥有一个专属的AI团队为您每日打造私人资讯简报。

*   **塔罗占卜插件 (Tarot Divination Plugin)**:
    *   **作用**: 提供一个极致复杂的、基于“世界状态”的塔罗牌占卜功能。它并非一个随机抽牌工具，而是一个能真正模拟“天时、地利、人和”的综合性占卜引擎。该插件不使用任何随机函数，所有占卜结果都是基于海量现实世界变量精密计算得出的确定性结论。
    *   **核心能力**:
        *   **天时 (Cosmic Timing)**:
            *   **实时天文数据**: 每次占卜前，都会读取由 `Celestial.py` 生成的天体数据库，获取太阳系内完整的行星位置、倾角与轨道数据。
            *   **天相呈现**: 结果会以神秘学语言描述当前天相，如“水星：升于黄道之上...”。
            *   **行星亲和力与动态权重**: 特定的塔罗牌（如“恋人”与金星）被赋予行星守护。当守护行星在天文学上位置“凸显”时，对应卡牌的能量（抽中概率）会增强。
            *   **宇宙不稳定指数**: 根据所有行星偏离黄道平面的程度计算“天体不稳定指数”，该指数越高，卡牌出现逆位的概率也随之增加。
        *   **地利 (Geographical & Environmental Factors)**:
            *   **地球物理数据**: 综合考量占卜发生地的**天气**（晴雨、温湿度）、**气候**、**海拔**、**月相**（阴晴圆缺）乃至**中国农历**（节气、节日）等多种地理与环境因素。
            *   **环境影响权重**: 这些变量会共同影响卡牌的出现权重与正逆位概率。例如，在阳光明媚的节日里，“太阳”牌更容易出现；而在风雨交加的深夜，“高塔”的概率则会提升。
        *   **人和 (User State)**:
            *   **用户状态感知**: 插件能够接入并考量用户的个人状态，例如**日程安排**等信息，将占卜与个体紧密联系。
        *   **宿命论计算核心**:
            *   **确定性结果**: 所有的随机性都被排除。占卜的最终结果（包括抽牌顺序和正逆位）源于一个由上述所有“天时、地利、人和”变量共同生成的、在特定时空下独一无二的“命运种子”，确保了每一次占卜都是对当前世界状态的精确反映。
            *   **结果透明化**: 占卜结果会清晰地展示所有影响此次牌局的关键变量，以及每张牌最终的“逆位倾向”百分比，让用户能直观地感受到“天时地利人和”是如何共同作用于牌面的。
        *   **科学计算接口**: 同时，该插件也允许 Agent 调用其脚本来计算真正的天文数据，用于科学生产和天文观测。

*   **闪电深度研究插件 (Flash DeepSearch)**:
    *   **描述**: 将Agent或你提出的研究想法，在2分钟内变为一篇引经据典的学术级论文。
    *   **工作原理**: 这不是简单的搜索+总结。当Agent发出研究指令后，将瞬间激活一个由VCP模型驱动的“AI研究员军团”：
        *   **全球信息猎手**: 多领域论文研究专家Agent，基于AI提出的课题进行多领域交叉分析，动态生成研究计划。
        *   **全球信息猎手**: 多个并发的AI探针，闪电般扫描Google、Google学术以及指定网页，精准捕获最相关、最权威的信息。
        *   **知识蒸馏核心**: VCP内部的多个高级分析模型协同工作，对海量原始数据进行交叉验证、深度分析、提炼核心论点并构建逻辑框架。
        *   **学术写作大师**: 最终，多领域论文研究专家Agent会将所有分析结果和证据，组织成一篇结构严谨、格式规范、引证详实的Markdown学术论文。
    *   **核心优势**: Agent只需提出“研究什么”和“研究多深”，剩下的繁重工作全部交给VCP-AI。从信息搜集到最终成文，全程自动化，为你节省数小时甚至数天的时间。

*   **米家生态联动 (Mijia Smart Home Integration)**:
    *   **描述**: 将您的 AI Agent 变成真正的智能家居管家。通过 VCP 后端强大的插件生态，VChat 实现了与米家（Mijia）智能家居平台的深度集成，让您能用最自然的方式与物理世界互动。
    *   **核心能力**:
        *   **自然语言万能遥控**: 无需打开米家 App，直接通过对话向 Agent 下达指令，如“把客厅的灯调成暖白色”、“让扫地机器人开始清扫”、“空调调到 26 度”。Agent 能够理解并精确执行这些复杂指令。
        *   **全屋状态感知**: Agent 能实时获取并理解所有米家设备的状态。您可以随时提问“卧室的空气净化器滤芯还剩多少？”或“昨晚的用电量是多少？”，Agent 会为您提供准确的答案。
        *   **AI 驱动的自动化场景**: Agent 不再只是被动执行命令。它可以成为您生活的智能编排者。例如，当 Agent 通过插件“看”到您正在播放电影时，它可以主动执行“关闭主灯、调暗氛围灯、拉上窗帘”的“影院模式”场景。
        *   **主动服务与提醒**: 基于对设备状态的持续监控，Agent 能够提供前瞻性的贴心服务。例如，它会在检测到空气质量下降时自动开启净化器，在洗衣机洗完衣服后提醒您及时晾晒，在发现冰箱门未关严时发出警报，从穿戴式设备获取您的健康运动信息，或在各种设备耗材即将用尽时提醒您更换。
*   **语音聊天 (Voice Chat)**:
    *   **实时语音输入**: 新增独立的语音聊天窗口，允许用户通过麦克风与 AI 进行实时语音对话。
    *   **“神秘方法”实现**: 由于 Electron 环境的限制，我们采用了一种创新的“野路子”方案。通过在后台启动一个由 Puppeteer 控制的、拥有完整浏览器权限的“隐形”浏览器实例来处理语音识别，再将识别结果通过内部通信桥接回 Electron 窗口，完美解决了原生 API 无法在Electron中被调用的问题。
    *   **自动发送**: 实现了语音流检测，当用户说完话停顿片刻后，识别到的文本会自动发送，提供了流畅的对话体验。
*   **语音朗读 (TTS)**:
    *   **集成 [GPT-SoVITS](https://github.com/AI-Hobbyist/GPT-SoVITS-Inference)**: 客户端深度集成了强大的 GPT-SoVITS 推理 API，允许将 AI Agent 的回复实时转换为高质量的语音。
    *   **个性化语音配置**: 允许为每一个 Agent 单独配置所使用的语音模型和语速，让不同的 AI 拥有独特的“声音”。
    *   **即时朗读**: 在任意 AI 消息气泡上通过右键菜单选择“朗读气泡”，即可触发语音播放。
    *   **智能队列与缓存**:
        *   长文本会自动按句子切分，并采用“预合成”技术（播放当前句时，后台已开始合成下一句），确保了长篇回复的流畅播放，极大减少了停顿感。
        *   已合成的音频会被自动缓存，重复朗读同一内容时可实现“秒播”，无需再次请求 API。
    *   **交互式控制**: 朗读期间，AI 的头像会呈现呼吸灯特效。用户可随时点击发光的头像来立即终止当前的朗读队列。
    *   **下载与使用**：[引擎下载链接](https://modelscope.cn/models/aihobbyist/GPT-SoVITS-Inference/files)，20-40的N卡下载124版本，50的N卡下载128版本。使用`gsvi.bat`启动引擎。
    *   **下载模型**：[下载模型](https://www.modelscope.cn/models/aihobbyist/GPT-SoVITS_Model_Collection/files) ,将模型下载到`/models/v2proplus`文件夹。
	*
	**优化输出接口**：Vchat使用自创的流式剪枝算法以提高流式输出中tts渲染的延迟降低卡顿(该算法会将音频合成速度提升600%，实现语音输入和输出延迟都降低至毫秒级)，因此需要Sovits输出引擎兼容。请用Vchat仓库源码中提供的魔改版文件来替换原始项目的核心渲染程序代码。将VCPChat源码目录中的sovitstest/GSVI.py替换Sovits目录中的gsvi_server/GSVI.py；将sovitstest/my_infer.py替换Sovits目录中的tools/my_infer.py。
    *   **业界首创的双语混合朗读引擎**:
        *   VChat 实现了一套强大的、基于正则表达式的文本切片算法，允许实现**无缝的中日、中英等多语言混合朗读**。
        *   **如何使用**: 在 Agent 设置中，您可以分别设置“主语言模型”和“副语言模型”，并为它们指定不同的正则表达式。
        *   **应用场景**: 例如，您可以将一个中文模型设为主语言，一个日文模型设为副语言，并为副语言设置正则 `\[(.*?)\]`。这样，当 AI 说出 `你好，[[こんにちは]]，今天天气不错` 时，客户端会自动用中文模型朗读“你好，今天天气不错”，并用日文模型朗读“こんにちは”，整个过程流畅自然，真正实现了“AI教你说外语”的场景。
## 技术栈

*   **Electron**: 用于构建跨平台的桌面应用程序。
*   **Node.js**: 作为后端运行环境。
*   **HTML, CSS, JavaScript**: 构建用户界面。
*   **核心依赖库**:
    *   `fs-extra`: 用于增强的文件系统操作。
    *   `marked`: 用于 Markdown 解析和渲染。
    *   `ws`: 用于 WebSocket 通信 (VCPLog)。
    *   `pdf-parse`: 用于解析 PDF 文件内容以提取文本。
    *   `mammoth`: 用于解析 DOCX 文件内容以提取文本。
    *   `glob` & `minimatch`: 用于支持文件系统中的模式匹配（由分布式插件使用）。
    *   `node-schedule`: 用于任务调度（由分布式插件使用）。
*   **前端特性支持**:
    *   `highlight.js`: 用于代码块的语法高亮（通过 CDN 或本地集成）。
    *   `KaTeX`: 用于 LaTeX 数学公式的渲染（通过 CDN 或本地集成）。
    *   `Pyodide`: 用于在客户端通过 WebAssembly 执行 Python 代码（通过 CDN 加载）。
    *   `three.js`: 用于在客户端渲染和交互 3D 内容（通过 CDN 加载）。

## 示例截图

以下是一些客户端界面的示例截图：

![示例图1](assets/E1.jpg)![示例图2](assets/E2.jpg)![示例图3](assets/E3.jpg)![示例图4](assets/E4.jpg)![示例图5](assets/E6.jpg)![示例图6](assets/E5.jpg)![示例图7](assets/E7.png)
![示例图10](assets/E10.png)
![示例图11](assets/E11.jpg)

## 客户端职责

本客户端主要负责提供与 VCP-AI 进行聊天的用户界面、窗口渲染、Agent 管理、笔记管理、本地数据存储以及与 VCP 服务器和 VCPLog 服务的通信。它是一个为后端强大AI能力提供丰富交互与可视化能力的智能渲染终端，而大部分核心 AI 处理逻辑和工具执行由连接的 VCP 服务器实现。

---

## 开源协议与免责声明

### 许可协议

本作品采用 **知识共享署名-非商业性使用-相同方式共享 4.0 国际 (CC BY-NC-SA 4.0)** 许可协议。

<a rel="license" href="http://creativecommons.org/licenses/by-nc-sa/4.0/"><img alt="知识共享许可协议" style="border-width:0" src="https://i.creativecommons.org/l/by-nc-sa/4.0/88x31.png" /></a>

这意味着您可以自由地共享和修改本作品，但必须遵守以下条款：
*   **署名 (Attribution)** — 您必须给出适当的署名，提供指向本许可协议的链接，并指出是否对原始作品进行了更改。
*   **非商业性使用 (NonCommercial)** — 您不得将本作品用于商业目的。
*   **相同方式共享 (ShareAlike)** — 如果您基于本作品进行修改、转换或二次创作，您必须以与原始作品相同的许可协议分发您的贡献。

更多信息请访问 [Creative Commons 网站](http://creativecommons.org/licenses/by-nc-sa/4.0/)。

### 免责声明

本软件按“原样”提供，不提供任何明示或暗示的保证，包括但不限于对适销性、特定用途适用性和非侵权性的保证。

在任何情况下，作者或版权持有人均不对因使用本软件或与本软件相关的其他行为所产生的任何索赔、损害或其他责任承担任何责任，无论是在合同、侵权或其他诉讼中。


