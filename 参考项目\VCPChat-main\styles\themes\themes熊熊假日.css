/*
 * styles/themes/themes熊熊假日.css
 * Curated by <PERSON><PERSON> for Professor <PERSON>
 * Bear's Holiday Theme
 * A cheerful and cozy theme inspired by summer fun and quiet nights.
 */

/* * =================================================================
 * Dark Theme: Forest Night (静谧林夜)
 * Inspired by bears camping by a fire in a dark forest.
 * A cozy, serene, and warm theme.
 * =================================================================
 */
:root {
    /* --- Wallpaper Interface --- */
    --chat-wallpaper-dark: url('../assets/wallpaper/forest_night.jpg');

    /* --- Base Colors --- */
    --primary-bg: #1a2a30;      /* 主背景 - 深青蓝色 */
    --secondary-bg: #2c3e4a;    /* 侧边栏/面板背景 - 石板灰蓝 */
    --tertiary-bg: #121e23;     /* 聊天区背景 - 近黑的深蓝 */
    --accent-bg: #3a506b;       /* 悬停/选中背景 - 柔和钢蓝 */
    --border-color: #3a506b;    /* 边框颜色 */
    --input-bg: #222e35;        /* 输入框背景 */

    --panel-bg-dark: rgba(44, 62, 74, 0.8); /* 基于--secondary-bg，增加80%不透明度 */

    /* --- Text Colors --- */
    --primary-text: #e8e8e8;    /* 主要文字 - 熊熊的米白色 */
    --secondary-text: #a0b8c8;  /* 次要/标题文字 - 柔和天空蓝 */
    --highlight-text: #ffcb77;  /* 高亮文字 - 温暖的篝火黄 */
    --text-on-accent: #ffffff;  /* 在强调色背景上的文字 */
    --placeholder-text: #6a7f8e; /* 输入框占位文字 */
    --quoted-text: #fd9e54;     /* 引用文本颜色 - 温暖的篝火橙 */
    --user-text: #e8e8e8;       /* 用户气泡文字 - 米白 */
    --agent-text: #e8e8e8;      /* Agent气泡文字 - 米白 */

    /* --- Bubble Colors (Frosted Glass Ready) --- */
    --user-bubble-bg: rgba(145, 109, 51, 0.573); /* 用户气泡 - 篝火黄 - 半透明 */
    --assistant-bubble-bg: rgba(44, 62, 74, 0.577); /* AI气泡 - 深石板灰蓝 - 半透明 */

    /* --- UI Element Colors --- */
    --button-bg: #3a506b;           /* 按钮背景 - 柔和钢蓝 */
    --button-hover-bg: #4a657a;      /* 按钮悬停 - 略亮的钢蓝 */
    --danger-color: #e57373;        /* 危险操作 - 柔和红 */
    --danger-hover-bg: #ef5350;
    --success-color: #70c1b3;       /* 成功操作 - 森林的青绿色 */
    --notification-bg: #2f3f4d;
    --notification-header-bg: #34495e;
    --notification-border: #ffcb77;
    --tool-bubble-bg: rgba(58, 80, 107, 0.2); /* VCP工具调用气泡背景 - 半透明钢蓝 */
    --tool-bubble-border: #ffcb77; /* VCP工具调用气泡边框 - 篝火黄 */

    /* --- Scrollbar --- */
    --scrollbar-track: rgba(44, 62, 74, 0.5);
    --scrollbar-thumb: rgba(100, 120, 130, 0.7);
    --scrollbar-thumb-hover: rgba(120, 140, 150, 0.6);

    /* --- Shimmer Effect for Loading --- */
    --shimmer-color-transparent: rgba(232, 232, 232, 0.408);
    --shimmer-color-highlight: rgba(232, 232, 232, 0.923);

    /* --- Text Shadow for Frosted Panels --- */
    --panel-text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);

    /* --- Unified Panel Background --- */
    --panel-bg: var(--panel-bg-dark);
}

body {
    background-image: var(--chat-wallpaper-dark);
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}

/* * =================================================================
 * Light Theme: Watermelon Day (西瓜假日)
 * Inspired by bears playing in a watermelon pool.
 * A bright, cheerful, and summery theme.
 * =================================================================
 */
body.light-theme {
    /* Wallpaper and background properties */
    background-image: var(--chat-wallpaper-light);
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    /* --- Wallpaper Interface --- */
    --chat-wallpaper-light: url('../assets/wallpaper/watermelon_day.jpg');

    /* --- Base Colors --- */
    --primary-bg: #b9dbb9d5;      /* 主背景 - 柔和薄荷绿 */
    --secondary-bg: #a2cccbcc;    /* 侧边栏 - 白色 */
    --tertiary-bg: #cff3fbe8;     /* 聊天区 - 浅天蓝色 */
    --accent-bg: #d0e8ef;       /* 悬停/选中背景 */
    --border-color: #d0e8ef;    /* 边框 */
    --input-bg: #ffffff;        /* 输入框 */

    --panel-bg-light: rgba(255, 255, 255, 0.85); /* 基于--secondary-bg，增加85%不透明度 */

    /* --- Text Colors --- */
    --primary-text: #3d405b;    /* 主要文字 - 深灰紫，像西瓜籽 */
    --secondary-text: #5c8d89;  /* 次要/标题文字 - 鼠尾草绿 */
    --highlight-text: #f25f5c;  /* 高亮文字 - 西瓜红 */
    --text-on-accent: #ffffff;  /* 强调背景文字 */
    --placeholder-text: #a0aab3; /* 占位文字 */
    --quoted-text: #4a90e2;     /* 引用文字 - 阳伞蓝 */
    --user-text: #3d405b;       /* 用户气泡文字 - 深灰紫 */
    --agent-text: #3d405b;      /* Agent气泡文字 - 深灰紫 */

    /* --- Bubble Colors --- */
    --user-bubble-bg: rgba(236, 202, 202, 0.488); /* 用户气泡 - 西瓜红 - 半透明 */
    --assistant-bubble-bg: rgba(233, 255, 233, 0.525); /* AI气泡 - 柔和薄荷绿 - 半透明 */

    /* --- UI Element Colors --- */
    --button-bg: #f25f5c;           /* 按钮 - 西瓜红 */
    --button-hover-bg: #e04b48;     /* 按钮悬停 - 深一点的红 */
    --danger-color: #e74c3c;        /* 危险色 - 红色 */
    --danger-hover-bg: #c0392b;
    --success-color: #70c1b3;       /* 成功色 - 清新绿，像西瓜皮 */
    --notification-bg: #e8f4f8;
    --notification-header-bg: #f0f0f0;
    --notification-border: #f25f5c;
    --tool-bubble-bg: rgba(224, 230, 235, 0.1); /* VCP工具调用气泡背景 - 半透明浅灰 */
    --tool-bubble-border: #f25f5c; /* VCP工具调用气泡边框 - 西瓜红 */

    /* --- Scrollbar --- */
    --scrollbar-track: rgba(200, 200, 200, 0.7);
    --scrollbar-thumb: rgba(150, 150, 150, 0.8);
    --scrollbar-thumb-hover: rgba(120, 120, 120, 0.6);

    /* --- Shimmer Effect --- */
    --shimmer-color-transparent: rgba(61, 64, 91, 0.04);
    --shimmer-color-highlight: rgba(61, 64, 91, 0.08);

    /* --- Unified Panel Background --- */
    --panel-bg: var(--panel-bg-light);
}

/* * =================================================================
 * Enhanced Frosted Glass Effect
 * 增强磨砂玻璃效果 - 小灵特制
 * =================================================================
 */

/* 为聊天气泡添加更强的磨砂玻璃效果 */
.message-bubble {
    backdrop-filter: blur(12px) saturate(120%);
    -webkit-backdrop-filter: blur(12px) saturate(120%);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬停时的微妙发光效果 */
.message-bubble:hover {
    backdrop-filter: blur(16px) saturate(130%);
    -webkit-backdrop-filter: blur(16px) saturate(130%);
    transform: translateY(-1px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 为工具调用气泡添加特殊效果 */
.tool-bubble {
    backdrop-filter: blur(10px) saturate(110%);
    -webkit-backdrop-filter: blur(10px) saturate(110%);
    border: 1px solid var(--tool-bubble-border);
    background: var(--tool-bubble-bg);
}

/* 深色主题下的特殊光效 */
:root .message-bubble {
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 浅色主题下的柔和阴影 */
body.light-theme .message-bubble {
    box-shadow: 
        0 4px 20px rgba(45, 41, 34, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.7);
}