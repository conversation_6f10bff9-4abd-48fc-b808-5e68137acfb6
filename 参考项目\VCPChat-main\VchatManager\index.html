<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VChat Manager</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="../styles/themes.css">
</head>
<body>
    <div id="app">
        <div id="sidebar">
            <div class="sidebar-header">
                <h2>VChat Manager</h2>
                <button id="theme-toggle">Toggle Theme</button>
            </div>
            <div id="agents-list"></div>
            <div id="groups-list"></div>
        </div>
        <div id="resizer"></div>
        <div id="main-content">
            <div id="tabs">
                <button class="tab-button active" data-tab="chat-history">Chat History</button>
                <button class="tab-button" data-tab="json-editor">JSON Editor</button>
                <button class="tab-button" data-tab="attachment-viewer">Attachments</button>
            </div>
            <div id="tab-content">
                <div id="chat-history" class="tab-panel active"></div>
                <div id="json-editor" class="tab-panel"></div>
                <div id="attachment-viewer" class="tab-panel"></div>
            </div>
        </div>
    </div>
    <script src="script.js"></script>
    <!-- Search Modal -->
    <div id="search-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Search Chat History</h3>
                <button id="close-search-modal" class="close-button">&times;</button>
            </div>
            <div class="modal-body">
                <input type="text" id="search-input" placeholder="Search for messages...">
                <div id="search-results"></div>
                <div id="search-pagination"></div>
            </div>
        </div>
    </div>
</body>
</html>