<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>音乐播放器</title>
    <!-- 确保主样式表在插件样式表之前加载 -->
    <link rel="stylesheet" href="../style.css"> 
    <link rel="stylesheet" href="music.css">
</head>
<body>
    <!-- 用于显示模糊专辑封面的背景 -->
    <div id="player-background"></div>

    <div class="music-player-container">
        <!-- 上方的播放器核心控制区 -->
        <div class="music-player glass-panel">
            <div class="track-info">
                <div class="album-art-wrapper">
                    <div class="album-art"></div>
                </div>
                <div class="track-details">
                    <div class="track-title">未选择歌曲</div>
                    <div class="track-artist">未知艺术家</div>
                </div>
                <button id="share-btn" class="control-btn" title="分享当前音频">
                    <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18 16.08C17.24 16.08 16.56 16.38 16.04 16.85L8.91 12.7C8.96 12.47 9 12.24 9 12C9 11.76 8.96 11.53 8.91 11.3L16.04 7.15C16.56 7.62 17.24 7.92 18 7.92C19.66 7.92 21 6.58 21 4.92C21 3.26 19.66 1.92 18 1.92C16.34 1.92 15 3.26 15 4.92C15 5.16 15.04 5.39 15.09 5.61L7.96 9.75C7.44 9.28 6.76 8.98 6 8.98C4.34 8.98 3 10.32 3 11.98C3 13.64 4.34 14.98 6 14.98C6.76 14.98 7.44 14.68 7.96 14.21L15.09 18.35C15.04 18.57 15 18.8 15 19.04C15 20.7 16.34 22.04 18 22.04C19.66 22.04 21 20.7 21 19.04C21 17.38 19.66 16.08 18 16.08Z" fill="currentColor"/></svg>
                </button>
            </div>

            <div class="progress-section">
                <canvas id="visualizer"></canvas>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress"></div>
                    </div>
                </div>
                <div class="time-display">
                    <span class="current-time">0:00</span>
                    <span class="duration">0:00</span>
                </div>
            </div>

            <div class="controls">
                <button id="mode-btn" class="control-btn icon-btn"></button>
                <button id="prev-btn" class="control-btn">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M19 5V19L12 12L19 5Z" fill="currentColor"/><path d="M5 5H7V19H5V5Z" fill="currentColor"/></svg>
                </button>
                <button id="play-pause-btn" class="control-btn play-btn">
                    <svg class="play-icon" width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 5V19L19 12L8 5Z" fill="currentColor"/></svg>
                    <svg class="pause-icon" width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 19H10V5H6V19ZM14 5V19H18V5H14Z" fill="currentColor"/></svg>
                </button>
                <button id="next-btn" class="control-btn">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5 5V19L12 12L5 5Z" fill="currentColor"/><path d="M17 5H19V19H17V5Z" fill="currentColor" transform="rotate(180 14.5 12)"/></svg>
                </button>
                <div class="volume-control">
                    <button id="volume-btn" class="control-btn">
                        <svg class="volume-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 9V15H7L12 20V4L7 9H3ZM16.5 12C16.5 10.23 15.48 8.71 14 7.97V16.03C15.48 15.29 16.5 13.77 16.5 12Z" fill="currentColor"/><path d="M14 3.23V5.29C16.89 6.15 19 8.83 19 12C19 15.17 16.89 17.85 14 18.71V20.77C18.01 19.86 21 16.28 21 12C21 7.72 18.01 4.14 14 3.23Z" fill="currentColor"/></svg>
                        <svg class="mute-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16.5 12C16.5 10.23 15.48 8.71 14 7.97V10.18L16.45 12.63C16.48 12.43 16.5 12.21 16.5 12ZM19 12C19 12.94 18.8 13.82 18.46 14.64L19.97 16.15C20.62 14.91 21 13.5 21 12C21 7.72 18.01 4.14 14 3.23V5.29C16.89 6.15 19 8.83 19 12ZM1.27 4.27L3 6.01V15H7L12 20V13.28L14.73 16.01C14.5 16.02 14.25 16.03 14 16.03V18.71C14.73 18.71 15.43 18.58 16.08 18.33L18.73 20.98L20 19.71L4.27 3L3 4.27L12 13.27V4L7 9H3V6.01L1.27 4.27ZM12 4L9.91 6.09L12 8.18V4Z" fill="currentColor"/></svg>
                    </button>
                    <input type="range" id="volume-slider" min="0" max="1" step="0.01" value="1">
                </div>
            </div>
        </div>

        <!-- 下方的播放列表区 -->
        <div class="playlist-container glass-panel">
            <div class="playlist-header">
                <h3>播放列表</h3>
                <div class="playlist-actions">
                    <input type="text" id="search-input" placeholder="搜索...">
                    <button id="add-folder-btn">添加</button>
                </div>
            </div>
            <ul id="playlist" class="playlist">
                <!-- 播放列表项将由 JS 动态生成 -->
            </ul>
            <div id="loading-indicator" class="loading-indicator" style="display: none;">
                <div class="spinner"></div>
                <div class="loading-text">
                    <span>正在扫描音乐...</span>
                    <div class="scan-progress-container" style="display: none;">
                        <div class="scan-progress-bar"></div>
                    </div>
                    <span class="scan-progress-label"></span>
                </div>
            </div>
        </div>
    </div>
    <!-- 确保 JS 在 body 结束前加载 -->
    <script src="music.js"></script>
</body>
</html>