{"name": "MusicController", "displayName": "音乐播放器控制器", "pluginType": "synchronous", "version": "1.0.0", "description": "通过指令控制VChat内置音乐播放器，实现播放、暂停、切歌等功能。", "author": "<PERSON><PERSON>", "license": "MIT", "entryPoint": {"command": "node music-controller.js"}, "communication": {"protocol": "stdio"}, "capabilities": {"invocationCommands": [{"command": "playSong", "description": "功能: 根据提供的歌曲名称，在用户的播放列表中搜索并播放对应的歌曲。\n参数:\n- songname (字符串, 必需): 要播放的歌曲的准确标题或部分标题。\n- command (字符串, 可选): 此字段为可选，可以忽略。程序会自动识别意图。\n调用格式:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」MusicController「末」,\ncommand:「始」playSong「末」,\nsongname:「始」星の余韻「末」\n<<<[END_TOOL_REQUEST]>>>"}]}}