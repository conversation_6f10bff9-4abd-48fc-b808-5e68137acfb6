# 🎵 AIPetV2 TTS功能升级计划

## 📋 项目概述

基于对原aipet项目TTS实现的深入分析，本文档制定了aipetv2 TTS系统的全面升级计划。通过将原aipet项目中经过验证的完整TTS功能迁移到aipetv2的优雅DDD架构中，实现功能完整性与架构优雅性的完美结合。

## 🔍 详细差距分析

### 当前aipetv2 TTS架构优势
- ✅ **现代DDD架构**：采用领域驱动设计，代码组织清晰
- ✅ **事件驱动通信**：使用事件总线进行异步组件通信
- ✅ **基础功能完整**：支持多种TTS服务（Edge TTS、GPT-SoVITS等）
- ✅ **异步处理**：使用async/await模式，避免线程复杂性
- ✅ **模块化设计**：清晰的服务分离和依赖注入

### 与原aipet项目的关键差距

#### 1. 音频处理能力差距 (🔴 严重)
| 功能特性 | 原aipet | aipetv2 | 差距等级 |
|---------|---------|---------|----------|
| **音频格式检测** | ✅ 支持WAV/MP3/OGG/FLAC | ❌ 完全缺失 | 🔴 严重 |
| **音频格式转换** | ✅ 自动转WAV用于口型同步 | ❌ 完全缺失 | 🔴 严重 |
| **临时文件管理** | ✅ 专门的TempFileManager | ⚠️ 简单实现 | 🟡 轻微 |

#### 2. Live2D集成差距 (🔴 严重)
| 功能特性 | 原aipet | aipetv2 | 差距等级 |
|---------|---------|---------|----------|
| **口型同步** | ✅ 完整的WavHandler集成 | ❌ 完全缺失 | 🔴 严重 |
| **表情联动** | ✅ 情感检测+表情切换 | ❌ 缺失 | 🔴 严重 |
| **动作触发** | ✅ TTS播放时触发动作 | ❌ 缺失 | 🟠 中等 |

#### 3. 队列管理差距 (🟠 中等)
| 功能特性 | 原aipet | aipetv2 | 差距等级 |
|---------|---------|---------|----------|
| **双队列系统** | ✅ 文本队列+音频队列 | ❌ 单一队列 | 🟠 中等 |
| **中断控制** | ✅ 完整的中断机制 | ⚠️ 基础控制 | 🟠 中等 |
| **播放锁机制** | ✅ 线程安全的播放锁 | ❌ 缺失 | 🟠 中等 |

#### 4. 文本处理差距 (🟡 轻微)
| 功能特性 | 原aipet | aipetv2 | 差距等级 |
|---------|---------|---------|----------|
| **Markdown清理** | ✅ 复杂的正则清理 | ❌ 缺失 | 🟠 中等 |
| **工具调用过滤** | ✅ 多格式工具块过滤 | ❌ 缺失 | 🟠 中等 |
| **HTML标签清理** | ✅ 图片标签等清理 | ❌ 缺失 | 🟡 轻微 |

## 🎯 升级目标

### 核心目标
1. **功能完整性**：将原aipet项目的完整TTS功能迁移到aipetv2
2. **架构优雅性**：保持aipetv2的DDD架构优势
3. **Live2D集成**：实现完整的口型同步和表情联动
4. **音频处理**：支持多格式音频检测、转换和处理
5. **队列管理**：实现高效的双队列TTS管理系统

### 成功指标
- **功能完整度**: 从当前60% → 目标95%
- **Live2D集成**: 实现与原aipet项目等同的口型同步效果
- **音频处理**: 支持WAV/MP3/OGG/FLAC等多种格式
- **系统稳定性**: TTS播放成功率 > 99%
- **响应性能**: TTS合成响应时间 < 2秒

## 📊 关键代码对比分析

### 原aipet项目的核心优势实现

#### 音频格式检测 (原aipet项目)
```python
def _detect_audio_format(self, audio_data: bytes) -> str:
    """检测音频格式"""
    if audio_data.startswith(b'RIFF') and audio_data[8:12] == b'WAVE':
        return "wav"
    elif audio_data.startswith(b'\xff\xfb') or audio_data.startswith(b'ID3'):
        return "mp3"
    elif audio_data.startswith(b'OggS'):
        return "ogg"
    elif audio_data.startswith(b'fLaC'):
        return "flac"
    else:
        return "unknown"
```

#### Live2D口型同步 (原aipet项目)
```python
def _start_lipsync(self, wav_file_path: str):
    """启动Live2D口型同步"""
    if hasattr(self.live2d_widget, 'start_lipsync_from_file'):
        self.live2d_widget.start_lipsync_from_file(wav_file_path)
        print(f"✓ 口型同步已启动: {wav_file_path}")
    elif hasattr(self.live2d_widget, 'start_lipsync'):
        if self.live2d_widget.start_lipsync(wav_file_path):
            print(f"✓ 口型同步已启动: {wav_file_path}")
```

#### 双队列管理 (原aipet项目)
```python
# 文本队列和音频队列的双队列系统
self.text_queue = queue.Queue()
self.audio_queue = queue.Queue()
self.playback_lock = threading.Lock()
```

### aipetv2当前实现状况
- **音频格式检测**: ❌ 完全缺失
- **Live2D集成**: ❌ 完全缺失
- **队列管理**: ⚠️ 仅有基础的事件驱动处理

## 🚀 升级实施方案

### 阶段一：核心音频处理增强 (优先级: 🔴 最高)

#### 1.1 音频格式检测与转换模块
**新增文件**: `aipetv2/aipet/infrastructure/audio/format_detector.py`

```python
class AudioFormatDetector:
    """音频格式检测器 - 基于原aipet项目实现"""

    @staticmethod
    def detect_format(audio_data: bytes) -> str:
        """检测音频格式"""
        if audio_data.startswith(b'RIFF') and audio_data[8:12] == b'WAVE':
            return "wav"
        elif audio_data.startswith(b'\xff\xfb') or audio_data.startswith(b'ID3'):
            return "mp3"
        elif audio_data.startswith(b'OggS'):
            return "ogg"
        elif audio_data.startswith(b'fLaC'):
            return "flac"
        else:
            return "unknown"

    @staticmethod
    def convert_to_wav(audio_data: bytes, source_format: str) -> bytes:
        """转换音频格式为WAV"""
        if source_format == "wav":
            return audio_data

        # 使用pydub进行格式转换
        from pydub import AudioSegment
        import io

        try:
            audio = AudioSegment.from_file(io.BytesIO(audio_data), format=source_format)
            wav_buffer = io.BytesIO()
            audio.export(wav_buffer, format="wav")
            return wav_buffer.getvalue()
        except Exception as e:
            logger.error(f"音频格式转换失败: {e}")
            return audio_data  # 返回原始数据作为fallback
```

#### 1.2 增强音频播放器
**修改文件**: `aipetv2/aipet/infrastructure/audio/player.py`

```python
class AudioPlayer:
    """增强的音频播放器 - 支持格式检测和转换"""

    async def play_with_format_detection(self, audio_data: bytes) -> Optional[float]:
        """支持格式检测的播放方法"""
        try:
            # 1. 检测音频格式
            from .format_detector import AudioFormatDetector
            audio_format = AudioFormatDetector.detect_format(audio_data)
            logger.info(f"检测到音频格式: {audio_format}")

            # 2. 如果需要，转换格式
            if audio_format != "wav":
                logger.info(f"转换音频格式: {audio_format} -> wav")
                audio_data = AudioFormatDetector.convert_to_wav(audio_data, audio_format)

            # 3. 保存临时文件并播放
            temp_file_path = await self._save_temp_audio_file(audio_data, "wav")
            duration = await self._play_audio_file(temp_file_path)
            await self._cleanup_temp_file(temp_file_path)

            return duration
        except Exception as e:
            logger.error(f"音频播放失败: {e}")
            return None
```

#### 1.3 临时文件管理器增强
**新增文件**: `aipetv2/aipet/infrastructure/storage/temp_file_manager.py`

```python
class TempFileManager:
    """临时文件管理器 - 基于原aipet项目实现"""

    def __init__(self):
        self.temp_dir = Path(tempfile.gettempdir()) / "aipetv2_tts"
        self.temp_dir.mkdir(exist_ok=True)
        self.max_age_hours = 24

    def create_temp_audio_file(self, audio_data: bytes, format: str) -> str:
        """创建临时音频文件"""
        timestamp = int(time.time() * 1000)
        filename = f"tts_audio_{timestamp}.{format}"
        file_path = self.temp_dir / filename

        with open(file_path, 'wb') as f:
            f.write(audio_data)

        logger.debug(f"创建临时音频文件: {file_path}")
        return str(file_path)

    def cleanup_expired_files(self):
        """清理过期的临时文件"""
        try:
            current_time = time.time()
            max_age_seconds = self.max_age_hours * 3600

            for file_path in self.temp_dir.glob("tts_audio_*"):
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > max_age_seconds:
                        file_path.unlink()
                        logger.debug(f"清理过期临时文件: {file_path}")
        except Exception as e:
            logger.error(f"清理临时文件失败: {e}")
```

### 阶段二：Live2D集成开发 (优先级: 🔴 最高)

#### 2.1 Live2D服务接口定义
**新增文件**: `aipetv2/aipet/core/services/live2d_service.py`

```python
class Live2DService(BaseService):
    """Live2D集成服务 - 提供TTS与Live2D的联动功能"""

    def __init__(self, foundation):
        super().__init__(foundation)
        self.live2d_widget = None
        self.is_lipsync_active = False

    async def initialize(self):
        """初始化Live2D服务"""
        try:
            self.live2d_widget = self._get_live2d_widget()
            if self.live2d_widget:
                logger.info("✅ Live2D服务初始化成功")
            else:
                logger.warning("⚠️ Live2D组件未找到，服务将以降级模式运行")
        except Exception as e:
            logger.error(f"❌ Live2D服务初始化失败: {e}")

    async def start_lipsync(self, wav_file_path: str, sensitivity: float = 3.0) -> bool:
        """启动口型同步"""
        if not self.live2d_widget:
            logger.warning("Live2D组件不可用，无法启动口型同步")
            return False

        try:
            # 调用Live2D组件的口型同步方法
            if hasattr(self.live2d_widget, 'start_lipsync_from_file'):
                success = self.live2d_widget.start_lipsync_from_file(wav_file_path)
            elif hasattr(self.live2d_widget, 'start_lipsync'):
                success = self.live2d_widget.start_lipsync(wav_file_path, sensitivity)
            else:
                logger.error("Live2D组件不支持口型同步方法")
                return False

            if success:
                self.is_lipsync_active = True
                logger.info(f"✅ 口型同步已启动: {wav_file_path}")

            return success
        except Exception as e:
            logger.error(f"启动口型同步时发生异常: {e}")
            return False

    async def stop_lipsync(self):
        """停止口型同步"""
        if not self.live2d_widget or not self.is_lipsync_active:
            return

        try:
            if hasattr(self.live2d_widget, 'stop_lipsync'):
                self.live2d_widget.stop_lipsync()
                self.is_lipsync_active = False
                logger.info("✅ 口型同步已停止")
        except Exception as e:
            logger.error(f"停止口型同步时发生异常: {e}")

    async def set_expression(self, expression_name: str) -> bool:
        """设置表情"""
        if not self.live2d_widget:
            return False

        try:
            if hasattr(self.live2d_widget, 'set_expression'):
                success = self.live2d_widget.set_expression(expression_name)
                if success:
                    logger.info(f"✅ 表情已设置: {expression_name}")
                return success
        except Exception as e:
            logger.error(f"设置表情时发生异常: {e}")
        return False
```

#### 2.2 TTS与Live2D联动实现
**修改文件**: `aipetv2/aipet/core/services/tts_service.py`

```python
class TTSService(BaseService):
    def __init__(self, foundation):
        super().__init__(foundation)
        # ... 现有初始化代码 ...
        self.live2d_service = None

    async def initialize(self):
        """初始化TTS服务"""
        # ... 现有初始化代码 ...

        # 获取Live2D服务
        self.live2d_service = self.foundation.get_service("live2d_service")
        if self.live2d_service:
            logger.info("✅ TTS服务已连接到Live2D服务")
        else:
            logger.warning("⚠️ Live2D服务不可用，TTS将以基础模式运行")

    async def _synthesize_and_play(self, text: str, service: str = None, speaker: str = None):
        """合成语音并播放（增加Live2D联动）"""
        try:
            logger.info(f"🎵 开始TTS合成: '{text[:50]}...'")

            # 1. 执行TTS合成
            audio_data = await self.tts_manager.synthesize(text, service, speaker)
            if not audio_data:
                raise Exception("TTS合成返回空数据")

            # 2. 检测并转换音频格式（为Live2D口型同步准备WAV文件）
            wav_file_path = await self._prepare_wav_for_lipsync(audio_data)

            # 3. 启动Live2D口型同步
            lipsync_started = False
            if self.live2d_service and wav_file_path:
                lipsync_started = await self.live2d_service.start_lipsync(wav_file_path)
                if lipsync_started:
                    logger.info("✅ Live2D口型同步已启动")

            # 4. 播放音频
            duration = await self.audio_service.play_audio(audio_data)
            logger.info(f"🎵 音频播放完成，时长: {duration:.2f}秒")

            # 5. 停止Live2D口型同步
            if self.live2d_service and lipsync_started:
                await self.live2d_service.stop_lipsync()
                logger.info("✅ Live2D口型同步已停止")

            # 6. 清理临时文件
            if wav_file_path:
                await self._cleanup_temp_wav_file(wav_file_path)

        except Exception as e:
            logger.error(f"❌ TTS播放失败: {e}")
            # 确保停止Live2D口型同步
            if self.live2d_service:
                await self.live2d_service.stop_lipsync()

    async def _prepare_wav_for_lipsync(self, audio_data: bytes) -> Optional[str]:
        """为Live2D口型同步准备WAV文件"""
        try:
            from ...infrastructure.audio.format_detector import AudioFormatDetector
            from ...infrastructure.storage.temp_file_manager import TempFileManager

            # 检测音频格式
            audio_format = AudioFormatDetector.detect_format(audio_data)

            # 转换为WAV格式（如果需要）
            if audio_format != "wav":
                wav_data = AudioFormatDetector.convert_to_wav(audio_data, audio_format)
            else:
                wav_data = audio_data

            # 保存为临时WAV文件
            temp_manager = TempFileManager()
            wav_file_path = temp_manager.create_temp_audio_file(wav_data, "wav")

            return wav_file_path
        except Exception as e:
            logger.error(f"准备WAV文件失败: {e}")
            return None
```

### 阶段三：高级队列管理系统 (优先级: 🟠 中等)

#### 3.1 双队列TTS管理器
**新增文件**: `aipetv2/aipet/core/tts/queue_manager.py`

```python
class TTSQueueManager:
    """TTS队列管理器 - 基于原aipet项目的双队列设计"""

    def __init__(self):
        self.text_queue = asyncio.Queue()      # 文本队列
        self.audio_queue = asyncio.Queue()     # 音频队列
        self.playback_lock = asyncio.Lock()    # 播放锁
        self.cancellation_event = asyncio.Event()  # 中断事件
        self.is_processing = False

    async def add_text(self, text: str, priority: int = 0):
        """添加文本到队列"""
        await self.text_queue.put({
            "text": text,
            "priority": priority,
            "timestamp": time.time()
        })

    async def process_text_queue(self):
        """处理文本队列（生产者）"""
        while not self.cancellation_event.is_set():
            try:
                # 获取文本任务
                text_task = await asyncio.wait_for(
                    self.text_queue.get(), timeout=1.0
                )

                # 执行TTS合成
                audio_data = await self._synthesize_text(text_task["text"])

                # 添加到音频队列
                await self.audio_queue.put({
                    "audio_data": audio_data,
                    "text": text_task["text"],
                    "timestamp": time.time()
                })

            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"处理文本队列失败: {e}")

    async def process_audio_queue(self):
        """处理音频队列（消费者）"""
        while not self.cancellation_event.is_set():
            try:
                # 获取音频任务
                audio_task = await asyncio.wait_for(
                    self.audio_queue.get(), timeout=1.0
                )

                # 使用播放锁确保串行播放
                async with self.playback_lock:
                    await self._play_audio_task(audio_task)

            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"处理音频队列失败: {e}")

    async def cancel_all(self):
        """取消所有队列中的任务"""
        self.cancellation_event.set()

        # 清空队列
        while not self.text_queue.empty():
            try:
                self.text_queue.get_nowait()
            except asyncio.QueueEmpty:
                break

        while not self.audio_queue.empty():
            try:
                self.audio_queue.get_nowait()
            except asyncio.QueueEmpty:
                break
```

### 阶段四：文本处理增强 (优先级: 🟡 轻微)

#### 4.1 文本清理器
**新增文件**: `aipetv2/aipet/core/tts/text_cleaner.py`

```python
class TTSTextCleaner:
    """TTS文本清理器 - 基于原aipet项目实现"""

    @staticmethod
    def clean_for_tts(text: str) -> str:
        """清理文本用于TTS"""
        # 1. 移除工具调用块
        text = TTSTextCleaner.remove_tool_blocks(text)

        # 2. 移除DailyNote块
        text = TTSTextCleaner.remove_dailynote_blocks(text)

        # 3. 清理HTML标签
        text = TTSTextCleaner.remove_html_tags(text)

        # 4. 清理Markdown语法
        text = TTSTextCleaner.remove_markdown_syntax(text)

        # 5. 标准化空白字符
        text = re.sub(r'\s+', ' ', text).strip()

        return text

    @staticmethod
    def remove_tool_blocks(text: str) -> str:
        """移除工具调用块"""
        # 移除 <<<[TOOL_REQUEST]>>> ... <<<[END_TOOL_REQUEST]>>> 块
        text = re.sub(r'<<<\[TOOL_REQUEST\]>>>.*?<<<\[END_TOOL_REQUEST\]>>>', '', text, flags=re.DOTALL)

        # 移除其他格式的工具调用块
        text = re.sub(r'```json\s*\{[^}]*"tool_name"[^}]*\}.*?```', '', text, flags=re.DOTALL)

        return text

    @staticmethod
    def remove_dailynote_blocks(text: str) -> str:
        """移除DailyNote块"""
        text = re.sub(r'```DailyNote.*?```', '', text, flags=re.DOTALL)
        return text

    @staticmethod
    def remove_html_tags(text: str) -> str:
        """移除HTML标签"""
        # 移除图片标签
        text = re.sub(r'<img[^>]*>', '', text)

        # 移除其他HTML标签
        text = re.sub(r'<[^>]+>', '', text)

        return text

    @staticmethod
    def remove_markdown_syntax(text: str) -> str:
        """移除Markdown语法"""
        # 移除标题标记
        text = re.sub(r'^#{1,6}\s+', '', text, flags=re.MULTILINE)

        # 移除粗体和斜体标记
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)
        text = re.sub(r'\*(.*?)\*', r'\1', text)

        # 移除链接
        text = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', text)

        # 移除代码块
        text = re.sub(r'```.*?```', '', text, flags=re.DOTALL)
        text = re.sub(r'`([^`]+)`', r'\1', text)

        return text
```

---

## 📅 实施时间表

### 第1周：核心音频处理功能
- [ ] **Day 1-2**: 音频格式检测器开发和测试
- [ ] **Day 3-4**: 音频格式转换功能实现
- [ ] **Day 5-6**: 临时文件管理器增强
- [ ] **Day 7**: 音频播放器升级和集成测试

### 第2周：Live2D集成开发
- [ ] **Day 1-2**: Live2D服务接口设计和实现
- [ ] **Day 3-4**: 口型同步功能集成
- [ ] **Day 5-6**: TTS与Live2D联动开发
- [ ] **Day 7**: 表情和动作联动测试

### 第3周：队列管理系统
- [ ] **Day 1-3**: 双队列管理器开发
- [ ] **Day 4-5**: 中断控制机制实现
- [ ] **Day 6-7**: 播放锁和线程安全测试

### 第4周：文本处理与优化
- [ ] **Day 1-2**: 文本清理器开发
- [ ] **Day 3-4**: 性能优化和错误处理完善
- [ ] **Day 5-7**: 单元测试编写和集成测试

### 第5周：集成测试与文档
- [ ] **Day 1-3**: 完整功能集成测试
- [ ] **Day 4-5**: 性能基准测试和优化
- [ ] **Day 6-7**: 用户文档编写和部署指南更新

---

## 🧪 测试策略

### 单元测试
- **音频格式检测准确性测试**: 验证各种音频格式的正确识别
- **音频转换质量测试**: 确保转换后的音频质量符合要求
- **队列管理并发安全测试**: 验证多线程环境下的队列操作安全性
- **文本清理功能测试**: 测试各种文本格式的清理效果

### 集成测试
- **TTS服务端到端测试**: 从文本输入到音频播放的完整流程
- **Live2D联动测试**: 验证口型同步和表情联动的准确性
- **多服务提供商切换测试**: 测试不同TTS服务间的无缝切换
- **错误恢复机制测试**: 验证各种异常情况下的系统恢复能力

### 性能测试
- **音频处理性能基准**: 测量音频检测、转换和播放的耗时
- **队列处理吞吐量测试**: 验证队列系统的处理能力
- **内存使用优化验证**: 确保系统内存使用在合理范围内
- **响应时间测试**: 测量从TTS请求到开始播放的延迟

---

## 📊 成功指标

### 功能完整度目标
- **当前**: 60% → **目标**: 95%
- 实现与原aipet项目功能对等

### 性能指标
- **TTS响应时间**: < 2秒
- **音频播放延迟**: < 100ms
- **口型同步精度**: > 90%
- **系统稳定性**: > 99%

### 用户体验
- **无缝的TTS播放体验**: 支持多格式音频自动处理
- **流畅的Live2D口型同步**: 实现与原aipet项目等同的效果
- **可靠的中断和恢复机制**: 支持用户随时停止和重新开始
- **直观的错误提示**: 提供清晰的错误信息和解决建议

---

## 🔧 技术依赖

### 新增依赖
```python
# 音频处理
pydub>=0.25.1          # 音频格式转换
librosa>=0.9.0         # 音频分析（可选）

# Live2D集成
live2d-py>=1.0.0       # Live2D Python绑定

# 异步队列
aiofiles>=0.8.0        # 异步文件操作
```

### 系统要求
- **Python**: 3.8+
- **存储空间**: 建议1GB+用于临时文件
- **图形环境**: 支持OpenGL（Live2D需要）

---

## 🚀 部署注意事项

### 配置更新
需要更新的配置项：
- TTS临时文件存储路径
- Live2D模型文件路径
- 音频格式转换参数
- 队列大小限制

### 向后兼容性
- 保持现有API接口不变
- 渐进式功能启用
- 优雅的降级机制

### 监控和日志
- 详细的TTS处理日志
- 性能指标监控
- 错误报告机制
- 用户行为分析

---

## 📝 总结

本升级计划将aipetv2的TTS功能从当前的60%完整度提升到95%，实现与原aipet项目的功能对等，同时保持aipetv2优雅的DDD架构设计。通过分阶段实施，确保升级过程的稳定性和可控性。

**预期收益**：
- ✅ **完整的TTS功能体验**: 支持多格式音频处理
- ✅ **流畅的Live2D口型同步**: 实现与原项目等同的效果
- ✅ **高性能的音频处理**: 优化的格式检测和转换
- ✅ **可靠的系统稳定性**: 完善的错误处理和恢复机制
- ✅ **优雅的代码架构**: 保持DDD设计原则

**风险控制**：
- 🛡️ **分阶段实施降低风险**: 每个阶段独立验证
- 🛡️ **完善的测试覆盖**: 单元测试、集成测试、性能测试
- 🛡️ **向后兼容性保证**: 不破坏现有功能
- 🛡️ **详细的回滚计划**: 确保可以快速恢复到稳定状态

通过这个升级计划，aipetv2将拥有与原aipet项目相当的TTS功能完整性，同时保持更优雅的架构设计，为用户提供更好的桌宠体验。
        color: "white"
    }
    
    MouseArea {
        anchors.fill: parent
        onClicked: {
            console.log("停止TTS播放:", messageData.id)
            if (typeof ttsManager !== 'undefined') {
                ttsManager.stopPlayback()
            }
        }
        
        hoverEnabled: true
        ToolTip.visible: containsMouse
        ToolTip.text: "点击停止播放"
        ToolTip.delay: 500
    }
}
```

#### 2.2 全局播放控制
在工具栏添加全局TTS控制：

```qml
// 工具栏TTS控制组
RowLayout {
    spacing: 4
    
    Button {
        id: globalTTSToggle
        Layout.preferredWidth: 32
        Layout.preferredHeight: 28
        text: ttsManager.isPlaying ? "⏸️" : "▶️"
        
        onClicked: {
            if (ttsManager.isPlaying) {
                ttsManager.pausePlayback()
            } else {
                ttsManager.resumePlayback()
            }
        }
        
        ToolTip.text: ttsManager.isPlaying ? "暂停TTS" : "继续TTS"
    }
    
    Button {
        text: "⏹️"
        Layout.preferredWidth: 32
        Layout.preferredHeight: 28
        
        onClicked: {
            ttsManager.stopPlayback()
        }
        
        ToolTip.text: "停止TTS播放"
    }
}
```

### 阶段3：右键菜单朗读功能

#### 3.1 消息右键菜单扩展
```qml
Menu {
    id: messageContextMenu
    
    property var targetMessage: null
    
    MenuItem {
        text: "🔊 朗读消息"
        visible: messageContextMenu.targetMessage && 
                messageContextMenu.targetMessage.role === "assistant"
        enabled: typeof ttsManager !== 'undefined' && ttsManager.isEnabled()
        
        onTriggered: {
            if (messageContextMenu.targetMessage) {
                console.log("朗读消息:", messageContextMenu.targetMessage.id)
                ttsManager.speakMessage(
                    messageContextMenu.targetMessage.content,
                    messageContextMenu.targetMessage.id
                )
            }
        }
    }
    
    MenuItem {
        text: "⏹️ 停止朗读"
        visible: messageContextMenu.targetMessage && 
                messageContextMenu.targetMessage.isTTSPlaying
        
        onTriggered: {
            ttsManager.stopPlayback()
        }
    }
    
    MenuSeparator {}
    
    MenuItem {
        text: "📋 复制消息"
        onTriggered: {
            if (messageContextMenu.targetMessage) {
                clipboard.setText(messageContextMenu.targetMessage.content)
            }
        }
    }
}
```

#### 3.2 消息组件集成
```qml
// 在消息组件中添加右键菜单
MouseArea {
    anchors.fill: parent
    acceptedButtons: Qt.RightButton
    
    onClicked: {
        if (mouse.button === Qt.RightButton) {
            messageContextMenu.targetMessage = messageData
            messageContextMenu.popup()
        }
    }
}
```

### 阶段4：事件驱动架构增强

#### 4.1 基于事件的状态管理
```python
# 修改：aipetv2/aipet/core/services/tts_service.py
class TTSService:
    def __init__(self, foundation, event_bus):
        # 现有初始化...
        self.cache_manager = TTSCacheManager(
            cache_dir=foundation.get_config("tts.cache_dir", "cache/tts"),
            max_size_mb=foundation.get_config("tts.cache_size_mb", 500)
        )
        self._playing_messages = set()  # 正在播放的消息ID
        self._playback_status = {}  # 播放状态跟踪
        
    async def speak_message(self, text: str, message_id: str = None, **kwargs) -> bool:
        """朗读指定消息"""
        try:
            # 停止其他正在播放的消息
            if self._playing_messages:
                await self.stop_playback()
            
            # 标记消息为播放状态
            if message_id:
                self._playing_messages.add(message_id)
                self._emit_playback_status(message_id, True)
            
            # 检查缓存
            cache_key = self.cache_manager.get_cache_key(text, **kwargs)
            cached_audio = await self.cache_manager.get_cached_audio(cache_key)
            
            if cached_audio:
                logger.info(f"✅ 使用TTS缓存: {cache_key[:8]}...")
                await self._play_cached_audio(cached_audio, message_id)
            else:
                # 合成新音频
                logger.info(f"🔄 合成新TTS音频: {text[:30]}...")
                audio_data = await self.tts_manager.synthesize(text, **kwargs)
                
                if audio_data:
                    # 异步保存到缓存
                    asyncio.create_task(
                        self.cache_manager.save_audio_cache(cache_key, audio_data)
                    )
                    await self._play_audio_data(audio_data, message_id)
                else:
                    raise Exception("TTS合成失败")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ TTS播放失败: {e}")
            if message_id:
                self._emit_playback_status(message_id, False)
                self._playing_messages.discard(message_id)
            return False
    
    def _emit_playback_status(self, message_id: str, is_playing: bool):
        """发布播放状态事件"""
        event = TTSPlaybackStatusEvent(message_id, is_playing)
        self.event_bus.emit(event.event_type, event.data, "tts_service")
```

#### 4.2 事件系统扩展
```python
# 新增：TTS播放状态事件
class TTSPlaybackStatusEvent(BaseEvent):
    def __init__(self, message_id: str, is_playing: bool):
        super().__init__()
        self.data = {
            "message_id": message_id,
            "is_playing": is_playing,
            "timestamp": time.time()
        }
    
    @property
    def event_type(self) -> str:
        return "tts_playback_status_changed"
```

## 📅 实施时间表

### 第1周：缓存系统基础
- [ ] 创建缓存管理器基础架构
- [ ] 实现MD5哈希键生成
- [ ] 基础的存储和读取功能
- [ ] 单元测试编写

### 第2周：缓存系统完善
- [ ] LRU淘汰策略实现
- [ ] 压缩存储功能
- [ ] 缓存清理机制
- [ ] 性能测试和优化

### 第3周：视觉反馈系统
- [ ] 消息气泡播放指示器
- [ ] 全局播放控制组件
- [ ] 动画效果实现
- [ ] UI测试和调优

### 第4周：右键菜单功能
- [ ] 消息右键菜单设计
- [ ] 朗读功能集成
- [ ] 用户交互测试
- [ ] 功能完善和bug修复

### 第5周：事件驱动架构增强
- [ ] 基于事件的状态管理
- [ ] 播放状态跟踪优化
- [ ] 事件系统扩展
- [ ] 集成测试

### 第6周：测试和优化
- [ ] 全面功能测试
- [ ] 性能基准测试
- [ ] 用户体验测试
- [ ] 文档更新

## 🔧 技术实现细节

### 缓存键生成策略
```python
def generate_cache_key(text: str, service: str, speaker: str, **params) -> str:
    """
    生成稳定的缓存键
    考虑因素：文本内容、TTS服务、音色、语速、音调等参数
    """
    # 标准化文本（去除多余空格、统一换行符）
    normalized_text = re.sub(r'\s+', ' ', text.strip())
    
    # 构建缓存数据
    cache_data = {
        "text": normalized_text,
        "service": service,
        "speaker": speaker,
        "params": {k: v for k, v in sorted(params.items()) if v is not None}
    }
    
    # 生成MD5哈希
    cache_string = json.dumps(cache_data, sort_keys=True, ensure_ascii=False)
    return hashlib.md5(cache_string.encode('utf-8')).hexdigest()
```

### 基于事件的状态管理机制
```python
class TTSEventBasedStatusManager:
    """基于事件驱动的TTS状态管理器"""

    def __init__(self, event_bus):
        self.event_bus = event_bus
        self._active_playbacks = {}  # message_id -> playback_info

        # 订阅TTS相关事件
        self.event_bus.subscribe("tts_synthesis_started", self._on_synthesis_started)
        self.event_bus.subscribe("tts_playback_started", self._on_playback_started)
        self.event_bus.subscribe("tts_playback_finished", self._on_playback_finished)

    def _on_synthesis_started(self, event_data):
        """TTS合成开始"""
        message_id = event_data.get("message_id")
        if message_id:
            self._emit_status_event(message_id, "synthesizing")

    def _on_playback_started(self, event_data):
        """播放开始"""
        message_id = event_data.get("message_id")
        if message_id:
            self._active_playbacks[message_id] = {
                "start_time": time.time(),
                "status": "playing"
            }
            self._emit_status_event(message_id, "playing")

    def _on_playback_finished(self, event_data):
        """播放结束"""
        message_id = event_data.get("message_id")
        if message_id:
            self._active_playbacks.pop(message_id, None)
            self._emit_status_event(message_id, "stopped")

    def _emit_status_event(self, message_id: str, status: str):
        """发布状态变化事件"""
        event_data = {
            "message_id": message_id,
            "status": status,
            "is_playing": status == "playing",
            "timestamp": time.time()
        }
        self.event_bus.emit("tts_status_changed", event_data, "tts_status_manager")
```

## 📊 预期效果

### 性能提升
- **缓存命中率**：预计60-80%的请求将命中缓存
- **响应时间**：首次合成后的重复请求响应时间 < 100ms
- **资源使用**：减少50%以上的TTS API调用
- **存储效率**：通过压缩存储节省60%磁盘空间

### 用户体验改善
- **状态可见性**：用户可直观了解TTS播放状态
- **操作便捷性**：一键停止、右键朗读等快捷操作
- **反馈及时性**：实时的播放状态指示和进度反馈
- **控制精确性**：支持单个消息的精确播放控制

### 架构优势保持
- **事件驱动优势**：保持aipetv2现有的优雅事件驱动架构
- **异步处理能力**：充分利用async/await的性能优势
- **模块化扩展**：在现有架构基础上无缝添加新功能
- **维护性提升**：避免VCPChat复杂队列系统的维护负担

## 🚨 风险评估与应对

### 技术风险
1. **缓存一致性**：不同参数组合可能导致缓存键冲突
   - **应对**：完善的键生成算法和冲突检测机制

2. **内存使用**：大量缓存可能占用过多内存
   - **应对**：LRU淘汰策略和可配置的缓存大小限制

3. **UI响应性**：复杂动画可能影响界面流畅度
   - **应对**：优化动画性能，提供动画开关选项

### 兼容性风险
1. **现有配置**：升级可能影响现有TTS配置
   - **应对**：提供配置迁移脚本和向后兼容支持

2. **第三方服务**：不同TTS服务的参数差异
   - **应对**：标准化参数接口和服务适配器

## 📈 成功指标

### 量化指标
- 缓存命中率 ≥ 60%
- TTS响应时间减少 ≥ 40%
- 系统崩溃率降低 ≥ 50%
- 用户操作步骤减少 ≥ 30%

### 质量指标
- 用户满意度调查 ≥ 4.5/5.0
- 功能使用率提升 ≥ 25%
- 支持请求减少 ≥ 20%

## 📚 参考资料

1. **VCPChat TTS实现**：`参考项目/VCPChat-main/modules/SovitsTTS.js`
2. **缓存设计模式**：LRU Cache、Write-Through Cache
3. **Qt/QML动画**：Qt Animation Framework
4. **音频处理**：PyAudio、pygame音频处理

## 🛠️ 开发环境准备

### 依赖库安装
```bash
# 缓存相关
pip install diskcache>=5.4.0
pip install aiofiles>=0.8.0

# 音频处理
pip install pydub>=0.25.1
pip install mutagen>=1.45.1

# 测试工具
pip install pytest-asyncio>=0.20.0
pip install pytest-mock>=3.10.0
```

### 开发工具配置
```json
// .vscode/settings.json
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests/"],
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true
}
```

## 📁 文件结构规划

```
aipetv2/aipet/core/tts/
├── __init__.py
├── cache/
│   ├── __init__.py
│   ├── manager.py           # 缓存管理器主类
│   ├── storage.py           # 存储后端接口
│   ├── cleaner.py           # 缓存清理策略
│   └── metrics.py           # 缓存性能指标
├── feedback/
│   ├── __init__.py
│   ├── status_manager.py    # 播放状态管理
│   └── event_emitter.py     # 状态事件发射器
└── enhanced_service.py      # 增强的TTS服务

aipetv2/aipet/ui/qml/components/
├── tts/
│   ├── TTSIndicator.qml     # TTS播放指示器
│   ├── TTSControls.qml      # TTS控制组件
│   └── TTSContextMenu.qml   # TTS右键菜单

tests/tts/
├── test_cache_manager.py
├── test_status_manager.py
└── test_integration.py
```

## 🧪 测试策略

### 单元测试覆盖
```python
# tests/tts/test_cache_manager.py
import pytest
import tempfile
from pathlib import Path
from aipet.core.tts.cache.manager import TTSCacheManager

@pytest.fixture
def cache_manager():
    with tempfile.TemporaryDirectory() as temp_dir:
        yield TTSCacheManager(temp_dir, max_size_mb=10)

@pytest.mark.asyncio
async def test_cache_hit_miss(cache_manager):
    """测试缓存命中和未命中"""
    text = "测试文本"
    service = "edge_tts"
    speaker = "zh-CN-XiaoyiNeural"

    # 首次请求应该未命中
    cache_key = cache_manager.get_cache_key(text, service, speaker)
    cached_audio = await cache_manager.get_cached_audio(cache_key)
    assert cached_audio is None

    # 保存音频到缓存
    test_audio = b"fake_audio_data"
    await cache_manager.save_audio_cache(cache_key, test_audio)

    # 再次请求应该命中
    cached_audio = await cache_manager.get_cached_audio(cache_key)
    assert cached_audio == test_audio

@pytest.mark.asyncio
async def test_cache_size_limit(cache_manager):
    """测试缓存大小限制"""
    # 添加超过限制的缓存数据
    large_audio = b"x" * (5 * 1024 * 1024)  # 5MB

    for i in range(3):  # 总共15MB，超过10MB限制
        cache_key = cache_manager.get_cache_key(f"text_{i}", "service", "speaker")
        await cache_manager.save_audio_cache(cache_key, large_audio)

    # 验证缓存清理是否工作
    total_size = cache_manager.get_total_cache_size()
    assert total_size <= cache_manager.max_size
```

### 集成测试
```python
# tests/tts/test_integration.py
@pytest.mark.asyncio
async def test_tts_with_cache_integration():
    """测试TTS服务与缓存的集成"""
    # 模拟完整的TTS流程
    tts_service = TTSService(mock_foundation, mock_event_bus)

    # 首次请求
    start_time = time.time()
    result1 = await tts_service.speak_message("测试文本", "msg_1")
    first_duration = time.time() - start_time

    # 第二次相同请求（应该使用缓存）
    start_time = time.time()
    result2 = await tts_service.speak_message("测试文本", "msg_2")
    second_duration = time.time() - start_time

    assert result1 and result2
    assert second_duration < first_duration * 0.5  # 缓存应该快很多
```

## 📊 性能监控

### 缓存性能指标
```python
# aipet/core/tts/cache/metrics.py
class CacheMetrics:
    def __init__(self):
        self.hits = 0
        self.misses = 0
        self.total_requests = 0
        self.cache_size_bytes = 0
        self.cleanup_count = 0

    @property
    def hit_rate(self) -> float:
        """缓存命中率"""
        if self.total_requests == 0:
            return 0.0
        return self.hits / self.total_requests

    @property
    def cache_size_mb(self) -> float:
        """缓存大小（MB）"""
        return self.cache_size_bytes / (1024 * 1024)

    def record_hit(self):
        """记录缓存命中"""
        self.hits += 1
        self.total_requests += 1

    def record_miss(self):
        """记录缓存未命中"""
        self.misses += 1
        self.total_requests += 1

    def to_dict(self) -> dict:
        """导出指标数据"""
        return {
            "hit_rate": self.hit_rate,
            "total_requests": self.total_requests,
            "cache_size_mb": self.cache_size_mb,
            "cleanup_count": self.cleanup_count
        }
```

### 监控面板集成
```qml
// 开发者调试面板
Rectangle {
    id: ttsDebugPanel
    visible: false  // 仅在调试模式显示

    Column {
        Text { text: "TTS缓存统计" }
        Text { text: "命中率: " + (ttsManager.cacheHitRate * 100).toFixed(1) + "%" }
        Text { text: "缓存大小: " + ttsManager.cacheSizeMB.toFixed(1) + " MB" }
        Text { text: "总请求: " + ttsManager.totalRequests }

        Button {
            text: "清空缓存"
            onClicked: ttsManager.clearCache()
        }
    }
}
```

## 🔄 迁移和部署

### 配置迁移脚本
```python
# scripts/migrate_tts_config.py
def migrate_tts_config():
    """迁移现有TTS配置到新版本"""
    old_config_path = "config/tts_config.json"
    new_config_path = "config/tts_enhanced_config.json"

    if Path(old_config_path).exists():
        with open(old_config_path, 'r', encoding='utf-8') as f:
            old_config = json.load(f)

        # 转换配置格式
        new_config = {
            "services": old_config.get("services", {}),
            "cache": {
                "enabled": True,
                "max_size_mb": 500,
                "cleanup_interval_hours": 24,
                "expire_days": 30
            },
            "ui": {
                "show_playback_indicator": True,
                "enable_right_click_menu": True,
                "animation_enabled": True
            }
        }

        with open(new_config_path, 'w', encoding='utf-8') as f:
            json.dump(new_config, f, indent=2, ensure_ascii=False)

        print(f"✅ 配置已迁移: {old_config_path} -> {new_config_path}")
```

### 渐进式部署策略
1. **阶段1**：仅启用缓存功能，保持现有UI
2. **阶段2**：添加基础视觉反馈，不影响现有操作
3. **阶段3**：启用右键菜单功能
4. **阶段4**：完整功能上线

## 📋 验收标准

### 功能验收
- [ ] 缓存系统正常工作，命中率 > 60%
- [ ] 播放状态指示器正确显示和隐藏
- [ ] 右键菜单朗读功能正常
- [ ] 全局播放控制功能正常
- [ ] 配置迁移无数据丢失

### 性能验收
- [ ] 缓存命中时响应时间 < 100ms
- [ ] UI动画流畅，无卡顿现象
- [ ] 内存使用增长 < 50MB
- [ ] 磁盘缓存大小可控制

### 兼容性验收
- [ ] 现有TTS服务正常工作
- [ ] 现有配置文件兼容
- [ ] 不同操作系统正常运行
- [ ] 各种音频格式支持

## � 核心代码示例

### 缓存管理器完整实现
```python
# aipet/core/tts/cache/manager.py
import asyncio
import gzip
import hashlib
import json
import time
from pathlib import Path
from typing import Optional, Dict, Any
import aiofiles
from dataclasses import dataclass

@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    file_path: Path
    size: int
    access_time: float
    create_time: float

class TTSCacheManager:
    """TTS缓存管理器"""

    def __init__(self, cache_dir: str, max_size_mb: int = 500):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.max_size = max_size_mb * 1024 * 1024
        self.metadata_file = self.cache_dir / "metadata.json"
        self.entries: Dict[str, CacheEntry] = {}
        self._lock = asyncio.Lock()

        # 加载现有缓存元数据
        asyncio.create_task(self._load_metadata())

    async def _load_metadata(self):
        """加载缓存元数据"""
        if self.metadata_file.exists():
            try:
                async with aiofiles.open(self.metadata_file, 'r') as f:
                    data = json.loads(await f.read())
                    for key, entry_data in data.items():
                        file_path = Path(entry_data['file_path'])
                        if file_path.exists():
                            self.entries[key] = CacheEntry(
                                key=key,
                                file_path=file_path,
                                size=entry_data['size'],
                                access_time=entry_data['access_time'],
                                create_time=entry_data['create_time']
                            )
            except Exception as e:
                logger.warning(f"加载缓存元数据失败: {e}")

    async def _save_metadata(self):
        """保存缓存元数据"""
        data = {}
        for key, entry in self.entries.items():
            data[key] = {
                'file_path': str(entry.file_path),
                'size': entry.size,
                'access_time': entry.access_time,
                'create_time': entry.create_time
            }

        async with aiofiles.open(self.metadata_file, 'w') as f:
            await f.write(json.dumps(data, indent=2))

    def get_cache_key(self, text: str, service: str, speaker: str, **params) -> str:
        """生成缓存键"""
        # 标准化文本
        normalized_text = ' '.join(text.split())

        cache_data = {
            "text": normalized_text,
            "service": service,
            "speaker": speaker,
            "params": {k: v for k, v in sorted(params.items()) if v is not None}
        }

        cache_string = json.dumps(cache_data, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(cache_string.encode('utf-8')).hexdigest()

    async def get_cached_audio(self, cache_key: str) -> Optional[bytes]:
        """获取缓存的音频"""
        async with self._lock:
            if cache_key not in self.entries:
                return None

            entry = self.entries[cache_key]
            if not entry.file_path.exists():
                # 文件不存在，清理元数据
                del self.entries[cache_key]
                return None

            try:
                # 更新访问时间
                entry.access_time = time.time()

                # 读取并解压音频数据
                async with aiofiles.open(entry.file_path, 'rb') as f:
                    compressed_data = await f.read()
                    return gzip.decompress(compressed_data)

            except Exception as e:
                logger.error(f"读取缓存失败: {e}")
                return None

    async def save_audio_cache(self, cache_key: str, audio_data: bytes):
        """保存音频到缓存"""
        async with self._lock:
            try:
                # 压缩音频数据
                compressed_data = gzip.compress(audio_data, compresslevel=6)

                # 生成文件路径
                cache_file = self.cache_dir / f"{cache_key}.wav.gz"

                # 写入文件
                async with aiofiles.open(cache_file, 'wb') as f:
                    await f.write(compressed_data)

                # 更新元数据
                current_time = time.time()
                self.entries[cache_key] = CacheEntry(
                    key=cache_key,
                    file_path=cache_file,
                    size=len(compressed_data),
                    access_time=current_time,
                    create_time=current_time
                )

                # 检查缓存大小并清理
                await self._cleanup_if_needed()

                # 保存元数据
                await self._save_metadata()

            except Exception as e:
                logger.error(f"保存缓存失败: {e}")

    async def _cleanup_if_needed(self):
        """如果需要则清理缓存"""
        total_size = sum(entry.size for entry in self.entries.values())

        if total_size <= self.max_size:
            return

        # 按访问时间排序，删除最少使用的
        sorted_entries = sorted(
            self.entries.values(),
            key=lambda x: x.access_time
        )

        for entry in sorted_entries:
            if total_size <= self.max_size * 0.8:  # 清理到80%
                break

            try:
                entry.file_path.unlink(missing_ok=True)
                del self.entries[entry.key]
                total_size -= entry.size
                logger.info(f"清理缓存: {entry.key}")
            except Exception as e:
                logger.error(f"清理缓存失败: {e}")

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_size = sum(entry.size for entry in self.entries.values())
        return {
            "total_entries": len(self.entries),
            "total_size_mb": total_size / (1024 * 1024),
            "max_size_mb": self.max_size / (1024 * 1024),
            "usage_percent": (total_size / self.max_size) * 100 if self.max_size > 0 else 0
        }
```

### TTS桥接器增强
```python
# aipet/ui/bridges/enhanced_tts_bridge.py
from PySide6.QtCore import QObject, Signal, Slot, Property
from typing import Dict, Any

class EnhancedTTSBridge(QObject):
    """增强的TTS桥接器"""

    # 信号定义
    playbackStatusChanged = Signal(str, bool)  # message_id, is_playing
    cacheStatsChanged = Signal('QVariant')     # cache statistics

    def __init__(self, tts_service):
        super().__init__()
        self.tts_service = tts_service
        self._playing_messages = set()
        self._cache_stats = {}

        # 连接TTS服务事件
        self.tts_service.event_bus.subscribe(
            "tts_playback_status_changed",
            self._on_playback_status_changed
        )

    def _on_playback_status_changed(self, event_data):
        """处理播放状态变化"""
        message_id = event_data.get("message_id")
        is_playing = event_data.get("is_playing", False)

        if is_playing:
            self._playing_messages.add(message_id)
        else:
            self._playing_messages.discard(message_id)

        self.playbackStatusChanged.emit(message_id, is_playing)

    @Slot(str, str)
    def speakMessage(self, text: str, message_id: str = ""):
        """朗读指定消息"""
        try:
            import asyncio
            import threading

            def run_async():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(
                    self.tts_service.speak_message(text, message_id)
                )
                loop.close()

            thread = threading.Thread(target=run_async)
            thread.daemon = True
            thread.start()

        except Exception as e:
            logger.error(f"朗读消息失败: {e}")

    @Slot()
    def stopPlayback(self):
        """停止所有播放"""
        try:
            self.tts_service.stop_all_playback()
        except Exception as e:
            logger.error(f"停止播放失败: {e}")

    @Slot(str, result=bool)
    def isMessagePlaying(self, message_id: str) -> bool:
        """检查消息是否正在播放"""
        return message_id in self._playing_messages

    @Property('QVariant', notify=cacheStatsChanged)
    def cacheStats(self):
        """缓存统计信息"""
        return self._cache_stats

    @Slot()
    def refreshCacheStats(self):
        """刷新缓存统计"""
        try:
            stats = self.tts_service.get_cache_stats()
            self._cache_stats = stats
            self.cacheStatsChanged.emit(stats)
        except Exception as e:
            logger.error(f"获取缓存统计失败: {e}")

    @Slot()
    def clearCache(self):
        """清空缓存"""
        try:
            self.tts_service.clear_cache()
            self.refreshCacheStats()
        except Exception as e:
            logger.error(f"清空缓存失败: {e}")
```

## �🚀 后续优化方向

### 短期优化（1-2个月）
1. **智能预缓存**：根据用户习惯预缓存常用文本
2. **缓存共享**：多用户间的缓存共享机制
3. **音质优化**：支持不同音质等级的缓存策略

### 长期规划（3-6个月）
1. **云端缓存**：支持云端缓存同步
2. **AI优化**：使用AI预测用户可能需要的TTS内容
3. **多语言支持**：优化多语言TTS的缓存策略

## 🎯 重要结论

### aipetv2架构优势确认

经过深入分析，**aipetv2的事件驱动架构比VCPChat的队列+线程模式更加先进**：

1. **无需队列管理优化**：aipetv2已经采用了更优雅的事件驱动模式
2. **异步处理优势**：async/await比传统回调+线程更高效
3. **架构现代化**：模块化设计和依赖注入比传统架构更易维护

### 升级重点调整

基于架构分析，升级重点调整为：
1. **🚀 最高优先级**：智能缓存系统（性能提升关键）
2. **🎨 高优先级**：视觉反馈系统（用户体验关键）
3. **🔧 中等优先级**：状态管理增强（功能完善）
4. **💡 功能增强**：右键菜单等便捷操作

**核心理念**：在保持aipetv2优秀架构的基础上，学习VCPChat的优秀特性，而不是盲目模仿其架构设计。

---

**文档版本**：v1.1 (已修正架构分析)
**创建日期**：2025-01-19
**最后更新**：2025-01-19
**负责人**：开发团队
**审核状态**：已修正

## 📞 联系信息

**项目负责人**：开发团队
**技术支持**：请在项目Issue中提出问题
**文档反馈**：欢迎提出改进建议
