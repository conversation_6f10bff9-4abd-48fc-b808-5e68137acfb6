#!/usr/bin/env python3
"""
TTS文本处理测试脚本

测试文本清理和分段功能，模拟实际的AI回复内容
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from aipet.infrastructure.text.cleaner import TTSTextCleaner
from aipet.infrastructure.text.segmenter import TTSTextSegmenter, SegmentationConfig


def test_ai_response_text_processing():
    """测试AI回复文本处理"""
    print("🧪 测试AI回复文本处理")
    print("="*60)
    
    # 模拟实际的AI回复内容（包含HTML图片标签）
    test_text = """涵，你好呀！我是你的专属AI助手芊芊，很高兴见到你！<img src="image://localimg/emoji_pack/可爱.gif" width="120" style="max-width: 120px; height: auto; vertical-align: middle; margin: 0 5px;">

今天有什么我可以帮助你的吗？我可以：

1. **回答问题** - 各种知识性问题
2. **协助工作** - 文档编写、代码分析等
3. **聊天陪伴** - 轻松愉快的对话

让我们开始吧！<img src="image://localimg/emoji_pack/开心.gif" width="100" style="max-width: 100px;">"""

    print(f"原始文本 ({len(test_text)} 字符):")
    print(f"'{test_text[:100]}...'")
    print()
    
    # 1. 测试文本清理
    print("🧹 步骤1: 文本清理")
    print("-" * 40)
    
    cleaner = TTSTextCleaner()
    cleaned_text = cleaner.clean_text(test_text)
    
    print(f"清理后文本 ({len(cleaned_text)} 字符):")
    print(f"'{cleaned_text}'")
    print()
    
    # 获取清理统计
    stats = cleaner.get_cleaning_stats()
    print(f"清理统计: {stats}")
    print()
    
    # 2. 测试文本分段
    print("✂️ 步骤2: 文本分段")
    print("-" * 40)
    
    config = SegmentationConfig(max_length=50, min_length=10)
    segmenter = TTSTextSegmenter(config)
    
    segments = segmenter.segment_text(cleaned_text)
    
    print(f"分段结果 ({len(segments)} 个分段):")
    for i, segment in enumerate(segments, 1):
        print(f"  分段 {i}: '{segment}' ({len(segment)} 字符)")
    print()
    
    # 获取分段统计
    seg_stats = segmenter.get_segmentation_stats()
    print(f"分段统计: {seg_stats}")
    print()
    
    # 3. 验证结果
    print("✅ 步骤3: 结果验证")
    print("-" * 40)
    
    # 检查是否有可播放的内容
    has_playable_content = bool(cleaned_text.strip())
    print(f"有可播放内容: {has_playable_content}")
    
    if has_playable_content:
        print(f"✅ 文本处理成功！清理后有 {len(cleaned_text)} 字符可用于TTS")
        if segments:
            print(f"✅ 分段处理成功！共 {len(segments)} 个分段")
            total_chars = sum(len(seg) for seg in segments)
            print(f"✅ 分段总字符数: {total_chars}")
        else:
            print("⚠️ 分段处理后无内容")
    else:
        print("❌ 文本处理后无可播放内容")
    
    return has_playable_content, cleaned_text, segments


def test_edge_cases():
    """测试边界情况"""
    print("\n🔍 测试边界情况")
    print("="*60)
    
    cleaner = TTSTextCleaner()
    
    # 测试用例
    test_cases = [
        ("纯HTML", "<img src='test.gif'><div>内容</div>"),
        ("纯Markdown", "# 标题\n**粗体** *斜体*"),
        ("工具调用", "<<<[TOOL_REQUEST]>>>tool_name:「始」测试「末」<<<[END_TOOL_REQUEST]>>>"),
        ("混合内容", "正常文本<img src='test.gif'>**粗体**正常文本"),
        ("空内容", ""),
        ("只有空白", "   \n\t  "),
        ("只有标点", "！？。，"),
    ]
    
    for name, text in test_cases:
        cleaned = cleaner.clean_text(text)
        print(f"{name}: '{text}' -> '{cleaned}' (有效: {bool(cleaned.strip())})")
    
    print()


if __name__ == "__main__":
    try:
        # 主要测试
        has_content, cleaned, segments = test_ai_response_text_processing()
        
        # 边界情况测试
        test_edge_cases()
        
        # 总结
        print("🎯 测试总结")
        print("="*60)
        if has_content:
            print("✅ TTS文本处理功能正常")
            print(f"✅ 可以正确处理包含HTML标签的AI回复")
            print(f"✅ 文本清理和分段功能工作正常")
        else:
            print("❌ TTS文本处理存在问题")
            print("❌ 需要检查文本清理规则")
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
