<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主题选择</title>
    <!-- The main theme file is now loaded first to provide base variables -->
    <link rel="stylesheet" href="../styles/themes.css"> 
    <link rel="stylesheet" href="themes-module.css">
</head>
<body>
    <div class="container">
        <h1>选择您的主题</h1>
        <div class="themes-grid" id="themesGrid">
            <!-- Theme cards will be dynamically generated by JS -->
        </div>
        <div class="theme-preview">
            <h2>实时预览</h2>
            <div class="preview-box" id="previewBox">
                <!-- Left Pane (Simulates Sidebar/Panels with Dark Wallpaper) -->
                <div class="preview-pane" id="preview-pane-1">
                    <div class="preview-wallpaper-bg" id="preview-wallpaper-1"></div>
                    <div class="mock-title"></div>
                    <div class="mock-text"></div>
                    <div class="mock-text"></div>
                    <!-- Buttons for Pane 1 (Dark Theme) -->
                    <div class="preview-button-container" id="preview-buttons-1">
                        <button class="preview-button">按钮</button>
                        <button class="preview-button alt">按钮</button>
                    </div>
                </div>
                <!-- Right Pane (Simulates Main Content Area with Light Wallpaper) -->
                <div class="preview-pane" id="preview-pane-2">
                    <div class="preview-wallpaper-bg" id="preview-wallpaper-2"></div>
                    <div class="mock-title"></div>
                    <div class="mock-text"></div>
                    <div class="mock-text"></div>
                    <div class="mock-text"></div>
                    <!-- Buttons for Pane 2 (Light Theme) -->
                    <div class="preview-button-container" id="preview-buttons-2">
                        <button class="preview-button">按钮</button>
                        <button class="preview-button alt">按钮</button>
                    </div>
                </div>
        </div>
        <button id="saveThemeBtn" class="save-button">应用并刷新</button>
    </div>
    <script src="themes.js"></script>
</body>
</html>