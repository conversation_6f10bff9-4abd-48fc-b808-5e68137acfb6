# FileOperator Plugin Configuration Template
# Copy this file to .env and modify the values as needed

# Security Settings - Define allowed directories for file operations
# Use absolute paths, separated by commas
# Leave empty to allow all directories (NOT RECOMMENDED for production)
ALLOWED_DIRECTORIES=/Users/<USER>/Documents,/Users/<USER>/Desktop,/Users/<USER>/Downloads

# Maximum file size for operations (in bytes)
# 10MB = 10 * 1024 * 1024 = 10485760
# 50MB = 52428800
MAX_FILE_SIZE=10485760

# Maximum number of files to return in directory listings
MAX_DIRECTORY_ITEMS=1000

# Maximum number of search results
MAX_SEARCH_RESULTS=100

# Enable debug logging (true/false)
DEBUG_MODE=true

# WebSocket settings for push notifications (if enabled)
WEBSOCKET_HOST=localhost
WEBSOCKET_PORT=6573

# File operation settings
ENABLE_RECURSIVE_OPERATIONS=true
ENABLE_HIDDEN_FILES=false

# Backup settings (optional)
CREATE_BACKUPS=false
BACKUP_DIRECTORY=/Users/<USER>/Documents/VCPToolBox/Plugin/FileOperator/backups

# Additional security options
# REQUIRE_CONFIRMATION=false  # Require confirmation for destructive operations
# LOG_ALL_OPERATIONS=true     # Log all file operations to a file
