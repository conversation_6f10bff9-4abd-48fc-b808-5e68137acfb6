/* Modernized Theme Chooser by Roo */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');

:root {
    --transition-fast: 0.2s ease;
    --transition-slow: 0.4s ease;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    margin: 0;
    padding: 20px;
    /* Removed background-color to let the main theme wallpaper show */
    color: var(--primary-text);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    box-sizing: border-box;
}

.container {
    width: 100%;
    max-width: 900px;
    margin: 0 auto;
    padding: 30px;
    background: var(--panel-bg, rgba(28, 37, 49, 0.85));
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
    backdrop-filter: blur(12px) saturate(180%);
    -webkit-backdrop-filter: blur(12px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: background-color var(--transition-slow);
}

h1 {
    text-align: center;
    color: var(--primary-text);
    margin-bottom: 30px;
    font-weight: 700;
    font-size: 2em;
}

.themes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.theme-card {
    border-radius: 12px;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-align: center;
    background: var(--secondary-bg);
    overflow: hidden;
    border: 2px solid transparent;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.theme-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.2);
}

.theme-card.selected {
    border-color: var(--button-bg, #007bff);
    box-shadow: 0 0 20px rgba(var(--button-bg-rgb, 0, 123, 255), 0.5);
}

.card-preview {
    height: 100px;
    width: 100%;
    border-bottom: 1px solid var(--border-color, #444);
    display: flex;
}

.card-preview-pane-1 {
    width: 50%;
    height: 100%;
}

.card-preview-pane-2 {
    width: 50%;
    height: 100%;
}

.theme-card h3 {
    margin: 0;
    padding: 15px 10px;
    font-size: 1em;
    font-weight: 600;
    color: var(--primary-text);
    background: var(--tertiary-bg, var(--secondary-bg));
}

/* Preview Section */
.theme-preview h2 {
    font-weight: 600;
    margin-bottom: 15px;
    border-bottom: 1px solid var(--border-color, #444);
    padding-bottom: 10px;
}

.preview-box {
    position: relative;
    height: 200px;
    border-radius: 10px;
    overflow: hidden;
    background-color: var(--primary-bg);
    display: flex;
    border: 1px solid var(--border-color, #444);
}

.preview-pane {
    position: relative; /* Needed for wallpaper positioning */
    width: 50%;
    height: 100%;
    transition: background-color var(--transition-slow);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px 20px 60px 20px; /* Increased padding-bottom for buttons */
    box-sizing: border-box;
    overflow: hidden; /* Ensure wallpaper doesn't bleed out */
}

.preview-wallpaper-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    transition: background-image var(--transition-slow);
    z-index: -1; /* Place wallpaper behind mock content */
    opacity: 1; /* Full opacity, will be overlaid by semi-transparent pane background */
}

#preview-pane-1 {
    background-color: var(--secondary-bg);
}

#preview-pane-2 {
    background-color: var(--primary-bg);
}

.preview-pane .mock-title {
    width: 80%;
    height: 12px;
    border-radius: 4px;
    background-color: var(--secondary-text);
    opacity: 0.5;
    margin-bottom: 15px;
}

.preview-pane .mock-text {
    width: 90%;
    height: 8px;
    border-radius: 4px;
    background-color: var(--secondary-text);
    opacity: 0.3;
    margin-bottom: 8px;
}

.preview-pane .mock-text:last-child {
    width: 60%;
}

.preview-button-container {
    position: absolute;
    bottom: 10px; /* Adjusted to be within the new padding */
    right: 20px;
    display: flex;
    gap: 10px;
}

.preview-button {
    padding: 8px 16px;
    border-radius: 6px;
    border: none;
    font-size: 0.9em;
    font-weight: 600;
    color: var(--text-on-accent);
    background-color: var(--button-bg);
    transition: background-color var(--transition-fast);
}

.preview-button.alt {
    background-color: transparent;
    border: 1px solid var(--button-bg);
    color: var(--button-bg);
}

/* Save Button */
.save-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 15px;
    margin-top: 30px;
    font-size: 1.1em;
    font-weight: bold;
    color: white;
    background: linear-gradient(45deg, var(--button-bg), var(--accent-bg, var(--button-bg)));
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-align: center; /* Centering text */
    letter-spacing: 1px;
}

.save-button:hover {
    opacity: 0.9;
    box-shadow: 0 5px 15px rgba(var(--button-bg-rgb, 0, 123, 255), 0.4);
    transform: translateY(-2px);
}

.save-button:active {
    transform: translateY(0);
    box-shadow: none;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
    display: none; /* Hide scrollbar */
}

::-webkit-scrollbar-track {
    background: var(--scrollbar-track);
}

::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover);
}