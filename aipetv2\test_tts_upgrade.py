#!/usr/bin/env python3
"""
TTS功能升级集成测试脚本

测试新增的TTS功能组件：
1. 音频格式检测和转换
2. Live2D集成
3. 队列管理
4. 文本处理（清理和分段）
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_audio_format_detection():
    """测试音频格式检测"""
    print("\n" + "="*50)
    print("🎵 测试音频格式检测")
    print("="*50)
    
    try:
        from aipet.infrastructure.audio.format_detector import AudioFormatDetector, AudioFormatValidator
        
        # 创建测试音频数据
        wav_header = b'RIFF\x24\x08\x00\x00WAVE'
        mp3_header = b'\xff\xfb\x90\x00'
        
        detector = AudioFormatDetector()
        validator = AudioFormatValidator()
        
        # 测试格式检测
        wav_format = detector.detect_format(wav_header)
        mp3_format = detector.detect_format(mp3_header)
        
        print(f"✅ WAV格式检测: {wav_format}")
        print(f"✅ MP3格式检测: {mp3_format}")
        
        # 测试格式验证
        wav_valid = validator.is_valid_for_tts(wav_header)
        mp3_valid = validator.is_valid_for_live2d(mp3_header)
        
        print(f"✅ WAV TTS验证: {wav_valid}")
        print(f"✅ MP3 Live2D验证: {mp3_valid}")
        
        return True
        
    except Exception as e:
        print(f"❌ 音频格式检测测试失败: {e}")
        return False


async def test_audio_conversion():
    """测试音频转换"""
    print("\n" + "="*50)
    print("🔄 测试音频转换")
    print("="*50)
    
    try:
        from aipet.infrastructure.audio.converter import AudioConverter
        
        converter = AudioConverter()
        
        # 创建简单的WAV测试数据
        test_audio_data = b'RIFF\x24\x08\x00\x00WAVE' + b'\x00' * 100
        
        # 测试Live2D优化转换
        live2d_data = converter.convert_for_live2d(test_audio_data)
        print(f"✅ Live2D转换: {len(test_audio_data)} -> {len(live2d_data)} 字节")
        
        # 测试TTS优化转换
        tts_data = converter.convert_for_tts(test_audio_data)
        print(f"✅ TTS转换: {len(test_audio_data)} -> {len(tts_data)} 字节")
        
        return True
        
    except Exception as e:
        print(f"❌ 音频转换测试失败: {e}")
        return False


async def test_temp_file_manager():
    """测试临时文件管理"""
    print("\n" + "="*50)
    print("📁 测试临时文件管理")
    print("="*50)
    
    try:
        from aipet.infrastructure.storage.temp_file_manager import TempFileManager
        
        manager = TempFileManager()
        
        # 测试创建临时文件
        test_data = b"test audio data"
        temp_file = manager.create_temp_file_with_hash(test_data, "wav")
        print(f"✅ 创建临时文件: {temp_file}")
        
        # 测试文件存在
        if os.path.exists(temp_file):
            print(f"✅ 文件存在验证通过")
        else:
            print(f"❌ 文件不存在")
            return False
        
        # 测试获取统计信息
        stats = manager.get_stats()
        print(f"✅ 统计信息: {stats}")
        
        # 测试清理
        manager.cleanup_old_files(max_age_hours=0)  # 立即清理
        print(f"✅ 文件清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 临时文件管理测试失败: {e}")
        return False


async def test_text_cleaner():
    """测试文本清理器"""
    print("\n" + "="*50)
    print("🧹 测试文本清理器")
    print("="*50)
    
    try:
        from aipet.infrastructure.text.cleaner import TTSTextCleaner
        
        cleaner = TTSTextCleaner()
        
        # 测试文本
        test_text = """
        # 这是标题
        
        这是一段包含**粗体**和*斜体*的文本。
        
        ```python
        print("这是代码块")
        ```
        
        <<<[TOOL_REQUEST]>>>
        tool_name:「始」测试工具「末」
        <<<[END_TOOL_REQUEST]>>>
        
        这是[链接](http://example.com)文本。
        
        <div>HTML标签</div>
        """
        
        # 执行清理
        cleaned_text = cleaner.clean_text(test_text)
        
        print(f"原文长度: {len(test_text)}")
        print(f"清理后长度: {len(cleaned_text)}")
        print(f"清理后文本: {cleaned_text[:100]}...")
        
        # 获取统计信息
        stats = cleaner.get_cleaning_stats()
        print(f"✅ 清理统计: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文本清理器测试失败: {e}")
        return False


async def test_text_segmenter():
    """测试文本分段器"""
    print("\n" + "="*50)
    print("✂️ 测试文本分段器")
    print("="*50)
    
    try:
        from aipet.infrastructure.text.segmenter import TTSTextSegmenter, SegmentationConfig
        
        # 创建配置
        config = SegmentationConfig(max_length=50, min_length=10)
        segmenter = TTSTextSegmenter(config)
        
        # 测试文本
        test_text = "这是第一句话。这是第二句话，比较长一些！这是第三句话？最后还有一句话，用来测试分段功能的效果。"
        
        # 执行分段
        segments = segmenter.segment_text(test_text)
        
        print(f"原文: {test_text}")
        print(f"分段数量: {len(segments)}")
        for i, segment in enumerate(segments):
            print(f"分段 {i+1}: {segment}")
        
        # 获取统计信息
        stats = segmenter.get_segmentation_stats()
        print(f"✅ 分段统计: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文本分段器测试失败: {e}")
        return False


async def test_queue_manager():
    """测试队列管理器"""
    print("\n" + "="*50)
    print("📋 测试队列管理器")
    print("="*50)
    
    try:
        from aipet.infrastructure.tts.queue_manager import TTSQueueManager, TTSTask, TTSTaskPriority
        
        manager = TTSQueueManager(max_text_queue_size=10, max_audio_queue_size=5)
        
        # 创建测试任务
        task1 = TTSTask(
            task_id="test_1",
            text="测试文本1",
            priority=TTSTaskPriority.NORMAL
        )
        
        task2 = TTSTask(
            task_id="test_2", 
            text="测试文本2",
            priority=TTSTaskPriority.HIGH
        )
        
        # 添加任务到队列
        await manager.add_text_task(task1)
        await manager.add_text_task(task2)
        
        print(f"✅ 添加了 2 个文本任务")
        
        # 获取统计信息
        stats = manager.get_statistics()
        print(f"✅ 队列统计: {stats}")
        
        # 停止管理器
        await manager.stop()
        print(f"✅ 队列管理器已停止")
        
        return True
        
    except Exception as e:
        print(f"❌ 队列管理器测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始TTS功能升级集成测试")
    print("="*60)
    
    test_results = []
    
    # 执行各项测试
    tests = [
        ("音频格式检测", test_audio_format_detection),
        ("音频转换", test_audio_conversion),
        ("临时文件管理", test_temp_file_manager),
        ("文本清理器", test_text_cleaner),
        ("文本分段器", test_text_segmenter),
        ("队列管理器", test_queue_manager),
    ]
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            test_results.append((test_name, False))
    
    # 输出测试结果汇总
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有测试通过！TTS功能升级成功！")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查相关组件")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
