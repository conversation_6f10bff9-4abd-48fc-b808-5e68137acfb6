{"name": "vcp-chat-desktop", "version": "1.0.0", "description": "一个为VCP服务器打造的AI聊天桌面客户端", "main": "main.js", "scripts": {"postinstall": "electron-rebuild -f -w selection-hook", "start": "electron .", "pack": "electron-rebuild -f -w selection-hook && electron-builder --dir", "dist": "electron-rebuild -f -w selection-hook && electron-builder"}, "keywords": ["VCP", "AI", "Cha<PERSON>", "Desktop", "Electron"], "author": "莱恩 (由小吉辅助生成)", "license": "MIT", "devDependencies": {"@electron/rebuild": "^3.7.2", "electron": "^36.0.0", "electron-builder": "^26.0.12", "node-addon-api": "^8.3.1"}, "dependencies": {"@3d-dice/dice-box": "^1.1.4", "animejs": "^4.0.2", "axios": "^1.10.0", "clipboard-event": "^1.6.0", "exceljs": "^4.4.0", "express": "^5.1.0", "fs-extra": "^11.2.0", "glob": "^10.0.0", "html2canvas": "^1.4.1", "iconv-lite": "^0.6.3", "mammoth": "^1.9.1", "marked": "^12.0.0", "minimatch": "^9.0.0", "music-metadata": "^11.4.0", "node-global-key-listener": "^0.3.0", "node-schedule": "^2.1.1", "pdf-parse": "^1.1.1", "pdf-poppler": "^0.2.1", "portfinder": "^1.0.37", "puppeteer": "^24.15.0", "selection-hook": "^0.9.23", "sharp": "^0.34.2", "three": "^0.178.0", "trash": "^9.0.0", "ws": "^8.17.0"}, "build": {"appId": "com.vcp.chatdesktop", "productName": "VCP聊天客户端", "files": ["main.js", "preload.js", "renderer.js", "main.html", "image-viewer.html", "text-viewer.html", "style.css", "assets/", "modules/**/*", "node_modules/**/*"], "directories": {"buildResources": "assets"}, "mac": {"category": "public.app-category.productivity", "icon": "assets/icon.icns"}, "win": {"target": "nsis", "icon": "assets/icon.ico"}, "linux": {"target": "AppImage", "icon": "assets"}}}