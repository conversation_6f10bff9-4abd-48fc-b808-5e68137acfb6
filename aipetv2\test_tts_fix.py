#!/usr/bin/env python3
"""
测试TTS修复是否生效
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_html_cleaning():
    """测试HTML标签清理功能"""
    print("🧪 测试HTML标签清理功能")
    print("="*60)
    
    # 模拟包含HTML标签的AI回复
    test_text = """涵，你好呀！我是你的专属AI助手芊芊，很高兴见到你！<img src="image://localimg/emoji_pack/可爱.gif" width="120" style="max-width: 120px; height: auto; vertical-align: middle; margin: 0 5px;">

现在是2025年7月27日晚上10点38分，六安市..."""

    print(f"原始文本 ({len(test_text)} 字符):")
    print(f"'{test_text[:100]}...'")
    print()
    
    try:
        from aipet.infrastructure.text.cleaner import TTSTextCleaner
        
        cleaner = TTSTextCleaner()
        cleaned_text = cleaner.clean_text(test_text)
        
        print(f"清理后文本 ({len(cleaned_text)} 字符):")
        print(f"'{cleaned_text}'")
        print()
        
        # 检查是否成功移除HTML标签
        has_html_tags = '<img' in cleaned_text or '<div' in cleaned_text
        print(f"是否还有HTML标签: {has_html_tags}")
        print(f"清理后是否有内容: {bool(cleaned_text.strip())}")
        
        if not has_html_tags and cleaned_text.strip():
            print("✅ HTML标签清理功能正常")
            return True
        else:
            print("❌ HTML标签清理功能异常")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_event_publishing():
    """测试事件发布逻辑"""
    print("\n🎯 测试事件发布逻辑")
    print("="*60)
    
    print("检查orchestrator.py中的事件发布:")
    
    try:
        # 读取orchestrator文件内容
        orchestrator_path = project_root / "aipet" / "application" / "conversation" / "orchestrator.py"
        
        if orchestrator_path.exists():
            with open(orchestrator_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否包含tts_request事件发布
            has_tts_request = 'tts_request' in content
            has_ai_response_finished = 'ai_response_finished' in content
            
            print(f"包含 tts_request 事件: {has_tts_request}")
            print(f"包含 ai_response_finished 事件: {has_ai_response_finished}")
            
            if has_tts_request:
                print("✅ orchestrator已配置发布tts_request事件")
            else:
                print("❌ orchestrator未配置发布tts_request事件")
                
            return has_tts_request
        else:
            print("❌ 找不到orchestrator.py文件")
            return False
            
    except Exception as e:
        print(f"❌ 检查事件发布逻辑时出现异常: {e}")
        return False


def test_tts_service_listeners():
    """测试TTS服务事件监听器"""
    print("\n🎧 测试TTS服务事件监听器")
    print("="*60)
    
    try:
        # 读取TTS服务文件内容
        tts_service_path = project_root / "aipet" / "core" / "services" / "tts_service.py"
        
        if tts_service_path.exists():
            with open(tts_service_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否包含事件监听器
            has_tts_request_listener = '_on_tts_request' in content
            has_tts_request_registration = 'tts_request", self._on_tts_request' in content
            
            print(f"包含 _on_tts_request 方法: {has_tts_request_listener}")
            print(f"注册 tts_request 监听器: {has_tts_request_registration}")
            
            if has_tts_request_listener and has_tts_request_registration:
                print("✅ TTS服务已配置tts_request事件监听器")
                return True
            else:
                print("❌ TTS服务未正确配置tts_request事件监听器")
                return False
        else:
            print("❌ 找不到tts_service.py文件")
            return False
            
    except Exception as e:
        print(f"❌ 检查TTS服务监听器时出现异常: {e}")
        return False


if __name__ == "__main__":
    print("🔧 TTS修复验证测试")
    print("="*80)
    
    # 运行所有测试
    test_results = []
    
    test_results.append(test_html_cleaning())
    test_results.append(test_event_publishing())
    test_results.append(test_tts_service_listeners())
    
    # 总结结果
    print("\n🎯 测试总结")
    print("="*80)
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    print(f"通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("✅ 所有测试通过！TTS修复应该生效")
        print("✅ 下次AI回复时应该能正确清理HTML标签并播放TTS")
    else:
        print("❌ 部分测试失败，需要进一步检查")
        
    print("\n📝 预期行为:")
    print("1. AI回复包含HTML标签时，TTS服务应收到tts_request事件")
    print("2. 文本经过清理后移除HTML标签")
    print("3. 清理后的纯文本用于TTS合成和播放")
    print("4. 日志应显示: '📝 提取的文本: ...' (长度 > 0，无HTML标签)")
