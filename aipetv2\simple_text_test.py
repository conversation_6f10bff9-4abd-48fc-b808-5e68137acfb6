#!/usr/bin/env python3
"""
简单的文本清理测试
"""

import re

def simple_html_clean(text):
    """简单的HTML标签清理"""
    # 移除HTML标签
    cleaned = re.sub(r'<[^>]+>', '', text)
    # 规范化空白字符
    cleaned = re.sub(r'\s+', ' ', cleaned)
    return cleaned.strip()

# 测试文本
test_text = """涵，你好呀！我是你的专属AI助手芊芊，很高兴见到你！<img src="image://localimg/emoji_pack/可爱.gif" width="120" style="max-width: 120px; height: auto; vertical-align: middle; margin: 0 5px;">

今天有什么我可以帮助你的吗？"""

print("原始文本:")
print(repr(test_text))
print()

print("清理后文本:")
cleaned = simple_html_clean(test_text)
print(repr(cleaned))
print()

print(f"原始长度: {len(test_text)}")
print(f"清理后长度: {len(cleaned)}")
print(f"有内容: {bool(cleaned.strip())}")
