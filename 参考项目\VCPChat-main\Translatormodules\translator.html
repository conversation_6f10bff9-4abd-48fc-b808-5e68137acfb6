<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>翻译</title>
    <!-- 假设主程序的 style.css 已经加载并定义了颜色变量 -->
<link rel="stylesheet" href="../styles/themes.css"> <!-- 引用主题样式 -->
    <link rel="stylesheet" href="../style.css">
    <!-- 链接到新的、统一风格的 CSS 文件 -->
    <link rel="stylesheet" href="translator.css">
</head>
<body>
    <div class="translator-container">
        <header class="translator-header">
            <h2>翻译助手</h2>
            <div class="translator-controls">
                <select id="targetLanguageSelect">
                    <option value="en">英语</option>
                    <option value="zh">中文</option>
                    <option value="ja">日语</option>
                    <option value="ko">韩语</option>
                    <option value="fr">法语</option>
                    <option value="de">德语</option>
                    <option value="es">西班牙语</option>
                </select>
                <input type="text" id="customPromptVar" placeholder="自定义提示词 (可选)">
                <button id="translateBtn">翻译</button>
            </div>
        </header>
        <main class="translator-main">
            <textarea id="sourceText" placeholder="输入要翻译的文本..." rows="10"></textarea>
            <!-- 包裹文本框和按钮的容器 -->
            <div class="output-area">
                <textarea id="translatedText" placeholder="翻译结果将显示在这里..." rows="10" readonly></textarea>
                <!-- 复制按钮 -->
                <button id="copyBtn" title="复制译文">
                    <!-- 初始图标 -->
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6">
                      <path d="M7.5 3.375c0-1.036.84-1.875 1.875-1.875h.375a3.75 3.75 0 0 1 3.75 3.75v1.875h-8.25V3.375Z" />
                      <path fill-rule="evenodd" d="M9 4.5v1.875h6V4.5a2.25 2.25 0 0 0-2.25-2.25h-1.5A2.25 2.25 0 0 0 9 4.5ZM5.625 3.375A3.375 3.375 0 0 0 2.25 6.75v10.5c0 1.864 1.511 3.375 3.375 3.375h12.75c1.864 0 3.375-1.511 3.375-3.375V6.75a3.375 3.375 0 0 0-3.375-3.375h-1.875v1.875c0 1.036-.84 1.875-1.875 1.875h-6.75C8.01 8.625 7.5 7.786 7.5 6.75V3.375H5.625Z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>
        </main>
    </div>
    <script src="translator.js"></script>
</body>
</html>
