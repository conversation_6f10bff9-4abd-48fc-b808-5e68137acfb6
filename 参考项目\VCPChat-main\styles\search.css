/* --- Search Modal Styles --- */

.search-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.65);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1001; /* Higher than other elements */
    backdrop-filter: blur(5px);
}

.search-modal-content {
    background-color: var(--secondary-bg);
    color: var(--primary-text);
    padding: 20px;
    border-radius: 12px;
    width: 85%;
    max-width: 800px;
    height: 90vh;
    max-height: 800px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 8px 30px rgba(0,0,0,0.2);
    border: 1px solid var(--border-color);
}

.search-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 15px;
    margin-bottom: 15px;
    flex-shrink: 0;
}

.search-modal-header h3 {
    margin: 0;
    font-size: 1.4em;
    color: var(--highlight-text);
}

.search-modal-close-button {
    background: none;
    border: none;
    font-size: 2rem;
    cursor: pointer;
    color: var(--secondary-text);
    line-height: 1;
    padding: 0 5px;
    transition: color 0.2s;
}
.search-modal-close-button:hover {
    color: var(--primary-text);
}


.search-modal-body {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    overflow: hidden;
}

#global-search-input {
    width: 100%;
    padding: 12px 15px;
    margin-bottom: 15px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    background-color: var(--input-bg);
    color: var(--primary-text);
    font-size: 1.1rem;
    box-sizing: border-box;
    flex-shrink: 0;
}
#global-search-input:focus {
    outline: none;
    border-color: var(--highlight-text);
    box-shadow: 0 0 5px var(--highlight-text-translucent);
}


#global-search-results {
    flex-grow: 1;
    overflow-y: auto;
    padding-right: 5px; /* space for scrollbar */
}

.search-result-item {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.2s;
    border-radius: 6px;
    margin-bottom: 5px;
}

.search-result-item:hover {
    background-color: var(--accent-bg);
}

.search-result-item .context {
    font-size: 0.85em;
    color: var(--secondary-text);
    margin-bottom: 6px;
}

.search-result-item .content {
    font-size: 1em;
    line-height: 1.4;
    word-wrap: break-word;
}

.search-result-item .content .name {
    font-weight: bold;
    color: var(--highlight-text);
}

.search-result-item .content strong {
    color: var(--highlight-text);
    background-color: var(--highlight-bg);
    padding: 1px 3px;
    border-radius: 3px;
}

#global-search-pagination {
    padding-top: 10px;
    margin-top: 10px;
    border-top: 1px solid var(--border-color);
    text-align: center;
    flex-shrink: 0;
}

.pagination-button {
    padding: 8px 16px;
    margin: 0 5px;
    border: 1px solid var(--border-color);
    background-color: var(--button-bg);
    color: var(--text-on-accent);
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.2s;
}
.pagination-button:hover:not(:disabled) {
    background-color: var(--button-hover-bg);
}

.pagination-button:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

/* This is for highlighting the message in the main chat window */
.message.message-highlight {
    box-shadow: 0 0 10px 4px var(--highlight-text) !important;
    border: 1px solid var(--highlight-text);
    transition: box-shadow 0.5s ease-in-out, border 0.5s ease-in-out;
}