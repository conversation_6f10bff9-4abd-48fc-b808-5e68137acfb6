<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音聊天</title>
    <link rel="stylesheet" href="../style.css">
    <link rel="stylesheet" href="voicechat.css">
    <link rel="stylesheet" href="../styles/messageRenderer.css">
    <meta http-equiv="Content-Security-Policy"
          content="default-src 'self' data:;
                   script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com;
                   style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com;
                   img-src * data: file: blob:;
                   media-src * data: file: blob:;
                   font-src 'self' https://cdn.jsdelivr.net;">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
</head>
<body class="main-content">
    <header class="chat-header">
        <img id="agentAvatar" src="../assets/default_avatar.png" alt="Agent Avatar" class="avatar">
        <h3 id="currentChatAgentName">语音聊天</h3>
        <div class="header-controls">
             <button id="close-btn-voicechat" class="window-control-button" title="关闭">
                <svg viewBox="0 0 10 10"><polygon points="10,1.01 8.99,0 5,3.99 1.01,0 0,1.01 3.99,5 0,8.99 1.01,10 5,6.01 8.99,10 10,8.99 6.01,5"></polygon></svg>
            </button>
        </div>
    </header>
    <div class="chat-messages-container">
        <div class="chat-messages" id="chatMessages">
            <!-- Messages will be rendered here -->
        </div>
    </div>
    <footer class="chat-input-area">
        <textarea id="messageInput" placeholder="输入消息或点击麦克风开始语音输入..." rows="1"></textarea>
        <button id="toggleInputModeBtn" title="切换输入模式">
            <svg id="keyboard-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="24" height="24">
                <path d="M20 5H4c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm-9 3h2v2h-2V8zm0 3h2v2h-2v-2zm-3-3h2v2H8V8zm0 3h2v2H8v-2zm-1 2H5v-2h2v2zm0-3H5V8h2v2zm9 7H8v-2h8v2zm0-4h-2v-2h2v2zm0-3h-2V8h2v2zm3 3h-2v-2h2v2zm0-3h-2V8h2v2z"/>
            </svg>
            <svg id="mic-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="24" height="24" style="display: none;">
                <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.49 6-3.31 6-6.72h-1.7z"/>
            </svg>
        </button>
        <button id="sendMessageBtn" title="发送消息">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="24" height="24"><path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/></svg>
        </button>
    </footer>
    <script type="module" src="../modules/messageRenderer.js"></script>
    <script type="module" src="voicechat.js"></script>
</body>
</html>