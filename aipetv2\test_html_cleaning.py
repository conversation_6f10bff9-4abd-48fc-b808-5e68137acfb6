#!/usr/bin/env python3
"""
测试HTML标签清理功能
"""

import re

def test_html_cleaning():
    """测试HTML标签清理"""
    
    # 测试文本（来自实际日志）
    test_text = """涵，你好呀！我是你的专属AI助手芊芊，很高兴见到你！<img src="image://localimg/emoji_pack/可爱.gif" width="120" style="max-width: 120px; height: auto; vertical-align: middle; margin: 0 5px;">

现在是2025年7月27日晚上10点38分，六安市的夜晚应该很安静吧！<img src="image://localimg/emoji_pack/芳乃Ciallo.gif" width="100" style="max-width: 100px;">

今天有什么我可以帮助你的吗？我可以：

1. **回答问题** - 各种知识性问题
2. **协助工作** - 文档编写、代码分析等  
3. **聊天陪伴** - 轻松愉快的对话

让我们开始吧！"""

    print("🧪 HTML标签清理测试")
    print("="*60)
    
    print(f"原始文本 ({len(test_text)} 字符):")
    print(f"'{test_text[:100]}...'")
    print()
    
    # 使用简单的正则表达式清理HTML标签
    cleaned_text = re.sub(r'<[^>]+>', '', test_text)
    
    # 规范化空白字符
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
    
    print(f"清理后文本 ({len(cleaned_text)} 字符):")
    print(f"'{cleaned_text}'")
    print()
    
    # 检查结果
    has_html = '<' in cleaned_text and '>' in cleaned_text
    has_content = bool(cleaned_text.strip())
    
    print(f"是否还有HTML标签: {has_html}")
    print(f"是否有可播放内容: {has_content}")
    
    if not has_html and has_content:
        print("✅ HTML标签清理成功！")
        print("✅ 文本可用于TTS播放")
    else:
        print("❌ HTML标签清理失败")
    
    return cleaned_text

if __name__ == "__main__":
    cleaned = test_html_cleaning()
    
    print("\n🎯 预期效果:")
    print("当AI回复包含HTML标签时，TTS服务应该:")
    print("1. 收到包含HTML标签的原始文本")
    print("2. 通过文本处理器清理HTML标签")
    print("3. 使用清理后的纯文本进行TTS合成")
    print("4. 成功播放语音")
    
    print(f"\n📝 清理后的文本示例:")
    print(f"'{cleaned[:100]}...'")
