/* Enhanced Rendering Styles for Message Renderer */

/* Keyframes for animations */
@keyframes vcp-bubble-background-flow-kf {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes vcp-bubble-border-flow-kf {
    0% { background-position: 0% 50%; }
    50% { background-position: 200% 50%; } /* Adjusted for more color travel */
    100% { background-position: 0% 50%; }
}

@keyframes vcp-icon-rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes vcp-icon-heartbeat {
    0% { transform: scale(1); opacity: 0.6; }
    50% { transform: scale(1.15); opacity: 0.9; }
    100% { transform: scale(1); opacity: 0.6; }
}

@keyframes vcp-toolname-color-flow-kf {
    0% { background-position: 0% 50%; }
    50% { background-position: 150% 50%; } /* Adjusted for smoother flow with 300% background-size */
    100% { background-position: 0% 50%; }
}

/* Loading dots animation */
@keyframes vcp-loading-dots {
  0%, 20% {
    color: rgba(0,0,0,0);
    text-shadow:
      .25em 0 0 rgba(0,0,0,0),
      .5em 0 0 rgba(0,0,0,0);
  }
  40% {
    color: currentColor; /* Or a specific color */
    text-shadow:
      .25em 0 0 rgba(0,0,0,0),
      .5em 0 0 rgba(0,0,0,0);
  }
  60% {
    text-shadow:
      .25em 0 0 currentColor, /* Or a specific color */
      .5em 0 0 rgba(0,0,0,0);
  }
  80%, 100% {
    text-shadow:
      .25em 0 0 currentColor, /* Or a specific color */
      .5em 0 0 currentColor; /* Or a specific color */
  }
}

.thinking-indicator-dots {
  display: inline-block;
  font-size: 1em; /* Match parent font-size by default */
  line-height: 1; /* Ensure it doesn't add extra height */
  vertical-align: baseline; /* Align with the text */
  animation: vcp-loading-dots 1.4s infinite;
}

/* 主气泡样式 - VCP ToolUse */
.vcp-tool-use-bubble {
    background: linear-gradient(145deg, #3a7bd5 0%, #00d2ff 100%) !important;
    background-size: 200% 200% !important;
    animation: vcp-bubble-background-flow-kf 20s ease-in-out infinite;
    border-radius: 10px !important;
    padding: 8px 15px 8px 35px !important;
    color: #ffffff !important;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    margin-top: 5px !important; /* Add space above the tool bubble */
    margin-bottom: 10px !important;
    position: relative;
    overflow: hidden;
    line-height: 1.6;
    display: block !important;
    width: -moz-fit-content;
    width: fit-content;
    max-width: 100%;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
    font-size: 0.95em !important;
    white-space: pre-wrap; /* Restore: Allow wrapping but preserve whitespace */
    word-break: break-all; /* Restore: Break long strings */
    transition: all 0.4s ease-in-out; /* Add transition for smooth hover effect */
}

/* Animated Border for VCP ToolUse */
.vcp-tool-use-bubble::after {
    content: "";
    position: absolute;
    box-sizing: border-box; 
    top: 0; left: 0; width: 100%; height: 100%;
    border-radius: inherit;
    padding: 2px; /* Border thickness */
    background: linear-gradient(60deg, #76c4f7, #00d2ff, #3a7bd5, #ffffff, #3a7bd5, #00d2ff, #76c4f7);
    background-size: 300% 300%;
    animation: vcp-bubble-border-flow-kf 7s linear infinite;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    z-index: 0; 
    pointer-events: none;
}

/* On hover, we don't need to force a width. `fit-content` will adjust to the new content. */
/* We can keep a max-width to prevent it from becoming too wide. */
.vcp-tool-use-bubble:hover {
    max-width: 90%; /* Just constrain the max width on hover */
}

/* New styles for summary and details sections */
.vcp-tool-summary {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.vcp-tool-details {
    max-height: 0;
    opacity: 0;
    overflow: hidden;
    font-size: 0; /* Use font-size to collapse content, preventing width issues */
    transition: max-height 0.5s ease-in-out, opacity 0.5s ease-in-out, margin-top 0.5s ease-in-out;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    margin-top: 0;
}

.vcp-tool-use-bubble:hover .vcp-tool-details {
    max-height: 500px; /* Allow space for content to show */
    opacity: 1;
    margin-top: 10px; /* Add space between summary and details */
    font-size: initial; /* Restore font-size on hover */
}

.vcp-tool-details pre {
    margin: 0;
    padding: 10px;
    color: #f0f0f0;
    white-space: pre-wrap;
    word-break: break-all;
    font-size: 0.85em;
    line-height: 1.5;
}


/* 内部 span 的重置 - VCP ToolUse */
.vcp-tool-use-bubble .vcp-tool-label,
.vcp-tool-use-bubble .vcp-tool-name-highlight {
    background: none !important;
    border: none !important;
    padding: 0 !important;
    margin: 0 !important;
    box-shadow: none !important;
    color: inherit !important;
    display: inline !important;
    font-family: inherit !important; /* Inherit from parent bubble */
    font-size: inherit !important; /* Inherit from parent bubble */
    vertical-align: baseline;
    position: relative;
    z-index: 1;
}

/* "VCP-ToolUse:" 标签 */
.vcp-tool-use-bubble .vcp-tool-label {
    font-weight: bold; color: #f1c40f; margin-right: 6px;
}

/* 工具名高亮 - VCP ToolUse */
.vcp-tool-use-bubble .vcp-tool-name-highlight {
    background: linear-gradient(90deg, #f1c40f, #ffffff, #00d2ff, #f1c40f) !important; 
    background-size: 300% 100% !important; 
    -webkit-background-clip: text !important;
    background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    text-fill-color: transparent !important;
    font-style: normal !important;
    font-weight: bold !important;
    padding: 1px 3px !important; 
    border-radius: 4px !important;
    animation: vcp-toolname-color-flow-kf 4s linear infinite; 
    margin-left: 2px; 
}

/* 左上角齿轮图标 - VCP ToolUse */
.vcp-tool-use-bubble::before {
    content: "⚙️";
    position: absolute;
    top: 8px;
    left: 10px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.75); 
    z-index: 2; 
    animation: vcp-icon-rotate 4s linear infinite;
    transform-origin: center center; 
}

/* 隐藏 VCP 气泡内的复制按钮 */
.vcp-tool-use-bubble code .code-copy { /* This might target <code> inside <pre class="vcp-tool-use-bubble"> */
    display: none !important;
}
 /* Also hide if copy button is direct child of the bubble (if no inner code element) */
.vcp-tool-use-bubble > .code-copy {
    display: none !important;
}
.vcp-tool-request-bubble > strong { display: none !important; } /* Hide "VCP工具调用:" strong tag if it was ever added */

/* --- Maid Diary Bubble Redesign --- */

/* Main container for the diary entry */
.maid-diary-bubble {
    background: #fdfaf6 !important; /* A very light, warm parchment color */
    border: 1px solid #eaddd0; /* A soft, paper-like border */
    border-radius: 8px !important;
    padding: 12px 18px 15px 48px !important; /* Adjusted padding for new icon placement */
    color: #5d4037 !important; /* A warm, dark brown for text */
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
    margin-top: 10px !important; /* Add space above the diary bubble */
    margin-bottom: 12px !important;
    position: relative;
    overflow: visible; /* Allow for potential pseudo-elements to peek out */
    line-height: 1.7;
    display: block !important;
    font-family: 'Georgia', 'Times New Roman', serif !important;
    font-size: 1em !important;
}

/* The decorative icon on the top left, changed to a quill */
.maid-diary-bubble::before {
    content: "✒️";
    position: absolute;
    top: 14px;
    left: 16px;
    font-size: 22px;
    opacity: 0.6;
    z-index: 2;
    transform: rotate(-15deg); /* Tilted for a more dynamic look */
    animation: none !important; /* Removing previous animation for a more static, elegant feel */
}

/* Removing the animated border for a cleaner look */
.maid-diary-bubble::after {
    display: none !important;
}

/* Header section containing title and date */
.diary-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    border-bottom: 1px solid #d7ccc8; /* A subtle separator line */
    padding-bottom: 6px;
    margin-bottom: 10px;
}

/* "Maid's Diary" title */
.diary-title {
    font-weight: bold;
    font-size: 1.1em;
    color: #6d4c41; /* A rich, brownish color */
    font-family: 'Georgia', 'Times New Roman', serif !important;
}

/* Date on the top right */
.diary-date {
    font-size: 0.85em;
    color: #a1887f; /* Lighter brown for secondary info */
    font-style: italic;
}

/* Container for the Maid's name info */
.diary-maid-info {
    margin-bottom: 12px;
    font-size: 0.9em;
    color: #8d6e63;
}

.diary-maid-label {
    font-weight: bold;
}

.diary-maid-name {
    font-style: italic;
    color: #a1887f;
    background: rgba(161, 136, 127, 0.08);
    padding: 1px 5px;
    border-radius: 4px;
}

/* The main content of the diary entry */
.diary-content {
    font-size: 0.95em;
    color: #4e342e; /* A dark, rich brown for readability */
    white-space: pre-wrap;
    word-break: break-word;
}

/* Hide copy button as it's not a code block */
.maid-diary-bubble > .code-copy {
    display: none !important;
}

/* HTML5 音频播放器样式 */
audio[controls] {
    background: transparent !important; /* 将背景设置为透明 */
    border: none !important; /* 移除边框 */
    border-radius: 10px !important;
    padding: 10px 15px !important;
    color: #ffffff !important;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    margin-bottom: 10px !important;
    display: block;
    width: 350px;
    position: relative; /* Added for pseudo-element positioning */
    overflow: hidden; /* Added to contain the pseudo-element */
    z-index: 1; /* Ensure audio player is above the pseudo-element */
}

/* Animated Border for Audio Player */
audio[controls]::after {
    content: "";
    position: absolute;
    box-sizing: border-box;
    top: 0; left: 0; width: 100%; height: 100%;
    border-radius: inherit;
    padding: 2px; /* Border thickness */
    background: linear-gradient(60deg, #76c4f7, #00d2ff, #3a7bd5, #ffffff, #3a7bd5, #00d2ff, #76c4f7); /* Same gradient as VCP ToolUse bubble */
    background-size: 300% 300%;
    animation: vcp-bubble-border-flow-kf 7s linear infinite; /* Same animation as VCP ToolUse bubble */
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    z-index: 0; /* Place behind the actual audio controls */
    pointer-events: none;
}

audio[controls]::-webkit-media-controls-panel {
    background: #ffffff !important;
    border-radius: 9px !important;
    margin: 5px !important;
    padding: 5px !important;
    box-sizing: border-box !important;
    position: relative; /* Ensure panel is above the pseudo-element */
    z-index: 2; /* Increase z-index for the panel to be on top of the pseudo-element */
}

audio[controls]::-webkit-media-controls-play-button,
audio[controls]::-webkit-media-controls-mute-button,
audio[controls]::-webkit-media-controls-fullscreen-button,
audio[controls]::-webkit-media-controls-overflow-button {
    filter: brightness(0.3) contrast(1.5) !important;
}

audio[controls]::-webkit-media-controls-current-time-display,
audio[controls]::-webkit-media-controls-time-remaining-display {
    color: #181818 !important;
    text-shadow: none !important;
}

audio[controls]::-webkit-media-controls-timeline {
    background-color:rgb(255, 255, 255) !important;
    border-radius: 4px !important;
    height: 6px !important;
    margin: 0 5px !important;
}

audio[controls]::-webkit-media-controls-timeline::-webkit-slider-thumb {
    background-color: #555555 !important;
    border: 1px solid rgba(0, 0, 0, 0.3) !important;
    box-shadow: 0 0 2px rgba(0,0,0,0.3) !important;
    height: 12px !important;
    width: 12px !important;
    border-radius: 50% !important;
}

audio[controls]::-webkit-media-controls-timeline::-moz-range-thumb {
    background-color: #555555 !important;
    border: 1px solid rgba(0, 0, 0, 0.3) !important;
    height: 12px !important;
    width: 12px !important;
    border-radius: 50% !important;
}

audio[controls]::-webkit-media-controls-timeline::-moz-range-track {
    background-color:rgb(255, 255, 255) !important;
    border-radius: 4px !important;
    height: 6px !important;
}

audio[controls]::-webkit-media-controls-volume-slider {
    background-color:rgb(255, 255, 255) !important;
    border-radius: 3px !important;
    height: 4px !important;
    margin: 0 5px !important;
}

audio[controls]::-webkit-media-controls-volume-slider::-webkit-slider-thumb {
    background-color: #555555 !important;
    border: 1px solid rgba(0,0,0,0.3) !important;
    height: 10px !important;
    width: 10px !important;
    border-radius: 50% !important;
}

/* Context Menu Item Colors */
.context-menu-item.danger-item {
   color:hsl(1, 83.80%, 61.20%) !important; /* Red */
}
.context-menu-item.danger-item:hover {
   background-color: rgba(229, 57, 53, 0.1) !important;
}
.context-menu-item.info-item {
   color:rgb(90, 171, 238) !important; /* Lighter Blue */
}
.context-menu-item.info-item:hover {
   background-color: rgba(30, 136, 229, 0.1) !important;
}
.context-menu-item.regenerate-text {
   color: #43A047 !important; /* Green for regenerate */
}
.context-menu-item.regenerate-text:hover {
   background-color: rgba(67, 160, 71, 0.1) !important;
}

/* Highlight for quoted text */
.md-content .highlighted-quote { /* Increased specificity */
   color: var(--quoted-text) !important; /* Use CSS variable and !important */
   display: inline !important; /* Ensure it behaves as an inline element and does not break the line */
   vertical-align: baseline; /* Align properly with the surrounding text */
   word-break: break-all; /* Allow breaking within the quote to prevent overflow and layout issues */
}

/* AI 发送的链接样式 */
.md-content a {
   color: #87CEEB !important; /* 柔和的天蓝色 */
}

/* Markdown Table Styles (Theme Aware) */
/* Define light theme variables as defaults */
:root {
    --table-border-color: var(--border-color);
    --table-text-color: var(--primary-text);
    --table-bg-color: rgba(var(--rgb-secondary-bg-dark, 40, 40, 44), 0.2);
    --table-header-bg-color: rgba(var(--rgb-secondary-bg-dark, 40, 40, 44), 0.3);
    --table-header-text-color: var(--highlight-text);
    --table-row-even-bg-color: transparent;
    --table-row-hover-bg-color: rgba(var(--rgb-secondary-bg-dark, 40, 40, 44), 0.4);
}

/* Define dark theme variables when .dark-theme (or lack of .light-theme) is active */
body:not(.light-theme) { /* Or just .dark-theme if that's how your theme switching works */
    --table-border-color: var(--border-color);
    --table-text-color: var(--primary-text);
    --table-bg-color: rgba(var(--rgb-secondary-bg-dark, 40, 40, 44), 0.2);
    --table-header-bg-color: rgba(var(--rgb-secondary-bg-dark, 40, 40, 44), 0.3);
    --table-header-text-color: var(--highlight-text);
    --table-row-even-bg-color: transparent;
    --table-row-hover-bg-color: rgba(var(--rgb-secondary-bg-dark, 40, 40, 44), 0.4);
}

body.light-theme {
    --table-border-color: var(--border-color);
    --table-text-color: var(--primary-text);
    --table-bg-color: rgba(var(--rgb-secondary-bg-light, 255, 255, 255), 0.6);
    --table-header-bg-color: rgba(var(--rgb-secondary-bg-light, 255, 255, 255), 0.7);
    --table-header-text-color: var(--highlight-text);
    --table-row-even-bg-color: transparent;
    --table-row-hover-bg-color: rgba(var(--rgb-secondary-bg-light, 255, 255, 255), 0.8);
}

.md-content table {
    border-collapse: collapse;
    margin: 1em 0;
    width: auto;
    border: 1px solid var(--table-border-color);
    color: var(--table-text-color);
    background-color: var(--table-bg-color);
}

.md-content th, .md-content td {
    border: 1px solid var(--table-border-color);
    padding: 10px 15px;
    text-align: left;
}

.md-content th {
    background-color: var(--table-header-bg-color);
    font-weight: bold;
    color: var(--table-header-text-color);
}

/* Optional: Re-enable for alternating rows if desired for both themes */
.md-content tr:nth-child(even) td {
   /* background-color: var(--table-row-even-bg-color); */ /* Commented out for now, can be enabled */
}

.md-content tr:hover td {
     background-color: var(--table-row-hover-bg-color);
}

/* NEW STYLES FOR IMAGE PLACEHOLDERS */
.image-placeholder {
     background-color: rgba(128, 128, 128, 0.1);
     border: 1px dashed rgba(128, 128, 128, 0.3);
     border-radius: 8px;
     display: flex;
     align-items: center;
     justify-content: center;
     font-size: 13px;
     color: #888;
     /* 过渡效果，让替换更平滑 */
     transition: all 0.3s ease;
}

.image-placeholder::before {
     /* content: "正在加载图片..."; */
     content: '';
     display: block;
     width: 24px;
     height: 24px;
     border: 3px solid rgba(128, 128, 128, 0.3);
     border-top-color: #888;
     border-radius: 50%;
     animation: vcp-icon-rotate 1s linear infinite;
}
