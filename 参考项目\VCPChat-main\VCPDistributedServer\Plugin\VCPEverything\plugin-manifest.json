{"name": "LocalSearchController", "displayName": "本地文件秒搜 (Everything)", "pluginType": "synchronous", "version": "1.1.0", "description": "通过调用 Everything 命令行工具 (es.exe) 在本地计算机上实现毫秒级文件搜索。", "author": "VCPToolBox Community", "license": "MIT", "entryPoint": {"command": "node local-search-controller.js"}, "communication": {"protocol": "stdio"}, "configSchema": {"EVERYTHING_ES_PATH": {"type": "string", "description": "Everything 命令行工具 (es.exe) 的绝对路径。", "default": "C:\\Program Files (x86)\\Everything\\es.exe"}, "DEBUG_MODE": {"type": "boolean", "description": "是否开启此插件的调试日志。", "default": false}}, "capabilities": {"invocationCommands": [{"command": "search", "description": "功能: 在整台计算机上快速搜索文件，返回文件路径列表。\n重要提示: 此工具的搜索结果(文件路径)可以作为 FileOperator 插件的输入参数，以读取或操作这些文件。\n参数:\n- query (字符串, 必需): 搜索关键词。支持 Everything 的高级搜索语法 (例如: \"path:D:\\videos\\ *.mp4 size:>1gb\")。\n- maxResults (数字, 可选, 默认100): 返回的最大结果数量。\n调用格式:\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」LocalSearchController「末」,\ncommand:「始」search「末」,\nquery:「始」VCP a.txt「末」,\nmaxResults:「始」50「末」\n<<<[END_TOOL_REQUEST]>>>"}]}}