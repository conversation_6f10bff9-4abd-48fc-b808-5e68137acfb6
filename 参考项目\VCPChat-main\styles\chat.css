/* styles/chat.css */

.chat-header {
    padding: 12px 20px;
    padding: 12px 20px;
    background-color: var(--panel-bg-dark); /* 使用半透明变量 */
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border-bottom: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 36px;
    flex-shrink: 0;
}
body.light-theme .chat-header {
    background-color: var(--panel-bg-light);
}
.chat-header h3 {
    margin: 0;
    font-size: 1.2em;
    color: var(--highlight-text);
    font-weight: 500;
    text-shadow: var(--panel-text-shadow);
}

.theme-icon {
    width: 26px; 
    height: 26px; 
}

#sun-icon {
    stroke: #FFD700; 
    fill: #FFD700;   
}

#moon-icon {
    stroke: #6495ED; 
    fill: #6495ED;   
}

.chat-messages-container {
    flex-grow: 1;
    overflow-y: auto; 
    display: flex; 
    flex-direction: column-reverse; 
}

.chat-messages {
    padding: 15px 20px;
    display: flex;
    flex-direction: column; 
}

/* --- Message Item QQ Style --- */
.message-item {
    margin-bottom: 18px; /* Increased margin for QQ style */
    max-width: 100%; 
    display: flex;
    align-items: flex-start; 
    gap: 10px; 
    position: relative; 
}

.message-item.user {
    flex-direction: row-reverse; 
}

.message-item.assistant {
    flex-direction: row; 
}

.chat-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0; 
    /* border: 1px solid var(--border-color); */ /* Original static border */
    border: 2px solid transparent; /* Default transparent border, thickness can be adjusted */
    transition: border-color 0.3s ease; /* Smooth transition if color changes */
    margin-top: 0px;
}

.name-time-block {
    display: flex;
    flex-direction: column;
    margin-top: 0; 
    line-height: 1.3;
}

.message-item.user .name-time-block {
    align-items: flex-end;
    /* margin-right: 5px; */ /* Removed as bubble is now below */
}

.message-item.assistant .name-time-block {
    align-items: flex-start;
    /* No specific margin needed here by default */
}

/* New wrapper for name/time and bubble column */
.details-and-bubble-wrapper {
    display: flex;
    flex-direction: column;
    gap: 4px; /* Small gap between name/time block and the bubble */
    flex-grow: 1; /* Allow this wrapper to take available horizontal space */
    min-width: 0; /* Fixes potential overflow issues with flex children */
}

/* Align content within the wrapper based on user/assistant */
.message-item.user .details-and-bubble-wrapper {
    align-items: flex-end; /* Aligns name/time and bubble to the right */
}

.message-item.assistant .details-and-bubble-wrapper {
    align-items: flex-start; /* Aligns name/time and bubble to the left */
}

.message-item .sender-name {
    /* font-weight: 500; */ /* Replaced by more specific bold below */
    font-weight: bold; /* Make all sender names bold */
    margin-bottom: 2px;
    font-size: 0.85em;
}

.message-item.assistant .sender-name {
    color: var(--highlight-text); 
}
.message-item.user .sender-name {
    color: var(--secondary-text); 
    /* opacity: 0.9; */ /* Remove opacity to make it more solid and highlighted */
}

.message-item .message-timestamp {
    font-size: 0.7em; 
    color: var(--secondary-text);
    opacity: 0.8;
}

/* .md-content is the actual bubble */
.message-item .md-content {
    padding: 10px 15px;
    border-radius: 10px;
    max-width: 82%; /* Default width when sidebar is closed */
    word-wrap: break-word;
    line-height: 1.5;
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    position: relative;
    backdrop-filter: blur(8px); /* Keep if desired */
    -webkit-backdrop-filter: blur(8px); /* Keep if desired */
    transition: max-width 0.4s ease-in-out; /* Smooth transition for width change */
}

/* When notifications sidebar is active, make bubbles wider to fill more of the narrower space */
.main-content.notifications-sidebar-active .message-item .md-content {
    max-width: 90%;
}

.message-item.user .md-content {
    background-color: var(--user-bubble-bg);
    color: var(--user-text);
    border-bottom-right-radius: 4px;
}
body.light-theme .message-item.user .md-content {
    background-color: var(--user-bubble-bg);
    color: var(--user-text);
}

.message-item.assistant .md-content {
    background-color: var(--assistant-bubble-bg);
    color: var(--agent-text);
    border-bottom-left-radius: 4px; 
}
body.light-theme .message-item.assistant .md-content {
    background-color: var(--assistant-bubble-bg);
    color: var(--agent-text);
    border: 1px solid #d0d8e0; 
}

/* System messages special layout */
.message-item.system.system-message-layout {
    justify-content: center; 
    padding: 8px 12px;
    border-radius: 10px;
    background-color: var(--accent-bg); 
    color: var(--secondary-text);
    max-width: 80%;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    /* Undo flex for system messages if they are simple */
    display: block; 
}
.message-item.system.system-message-layout .md-content {
    width: 100%;
    max-width: 100%; /* Override bubble max-width for system */
    box-shadow: none;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    background-color: transparent;
    padding: 0;
}

/* Welcome bubble for initial "no item selected" message */
.message-item.system.welcome-bubble {
    background-color: rgba(var(--rgb-secondary-bg-dark, 40, 40, 44), 0.65); /* Default to dark theme's secondary bg with opacity */
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(var(--rgb-secondary-bg-dark, 40, 40, 44), 0.3);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    color: var(--primary-text);
    padding: 8px 20px; /* Minimal vertical padding */
    border-radius: 12px;
    max-width: 500px;
    margin: 30px auto;
    display: block; /* Changed from inline-block, parent is already block and centered */
    text-align: center;
}

body.light-theme .message-item.system.welcome-bubble {
    background-color: rgba(var(--rgb-secondary-bg-light, 255, 255, 255), 0.7);
    border: 1px solid rgba(var(--rgb-secondary-bg-light, 255, 255, 255), 0.4);
    color: var(--primary-text-light);
}

.message-item.system.welcome-bubble p {
    margin: 0; /* Ensure no vertical margin on the paragraph itself */
    padding: 0; /* Ensure no vertical padding on the paragraph itself */
    line-height: 1.2; /* Reset to a more standard single line height */
    font-size: 1em;
}
/* Removed p:last-child rule as it's no longer necessary */


.message-item.thinking .md-content .thinking-indicator,
.message-item.streaming .md-content .thinking-indicator {
    display: inline-block; 
    padding: 5px 0; 
}

.message-controls {
    position: absolute;
    top: 2px;
    /* Adjust right/left based on user/assistant */
    display: none; 
    z-index: 10;
}
.message-item.user .message-controls {
    left: 5px; /* For user messages if bubble is on the left */
}
.message-item.assistant .message-controls {
    right: 5px;
}

.message-item:hover .message-controls {
    display: flex; 
}

.message-edit-btn {
    background: rgba(255,255,255,0.1);
    border: none;
    color: var(--primary-text);
    padding: 3px 6px; 
    font-size: 0.8em;
    border-radius: 5px; 
    cursor: pointer;
    margin-left: 5px;
}
.message-edit-btn:hover {
    background: rgba(255,255,255,0.2);
}


.message-item .md-content img,
.message-item .md-content video,
.message-item .md-content audio {
    max-width: 100%;
    border-radius: 8px;
    margin-top: 8px;
}
.message-item .md-content pre { 
    background-color: rgba(42, 45, 53, 0.85); /* Slightly transparent, bluish-dark background */
    padding: 10px;
    border-radius: 6px;
    white-space: pre-wrap; 
    word-break: break-all;   
    overflow-x: auto; 
    font-size: 0.9em;
    margin-top: 8px;
    border: 1px solid var(--border-color);
}
/* Attachments inside the bubble */
.message-item .md-content .message-attachments {
    display: flex;
    flex-wrap: wrap; 
    gap: 8px; 
    margin-top: 8px;
}

.topic-timestamp-bubble {
    display: none; 
    text-align: center;
    padding: 3px 12px; 
    font-size: 0.75em; 
    color: var(--secondary-text);
    background-color: var(--accent-bg); 
    border-radius: 12px; 
    margin: 6px auto 8px auto; 
    max-width: fit-content; 
    box-shadow: 0 1px 2px rgba(0,0,0,0.1); 
    opacity: 0.8; 
}


.vcp-tool-request-bubble { /* This is for the old style, might conflict or be replaced by new .vcp-tool-use-bubble */
    background-color: var(--tool-bubble-bg);
    border: 1px solid var(--tool-bubble-border);
    border-radius: 8px;
    padding: 10px 15px;
    margin-top: 10px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 0.9em;
    color: #c5c8c6; 
    white-space: pre-wrap; 
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}
.vcp-tool-request-bubble strong { 
    color: #81a2be; 
}
.vcp-tool-request-bubble span.vcp-param-value { 
    color: #b5bd68; 
}


.chat-input-area {
    padding-top: 8px;
    padding-right: 15px;
    padding-bottom: 12px;
    padding-left: 15px;
    padding-top: 8px;
    padding-right: 15px;
    padding-bottom: 12px;
    padding-left: 15px;
    background-color: var(--panel-bg-dark); /* 使用半透明变量 */
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    display: flex;
    align-items: flex-end;
    flex-wrap: wrap;
    flex-shrink: 0; /* Prevent input area from shrinking */
}
body.light-theme .chat-input-area {
    background-color: var(--panel-bg-light);
}

.attachment-preview-area .file-preview { /* This is the old attachment preview style in input area */
    background-color: var(--accent-bg);
    padding: 3px 6px;
    border-radius: 4px;
    font-size: 0.8em;
    display: flex;
    align-items: center;
}
.attachment-preview-area .file-preview button {
    background: none;
    border: none;
    color: var(--danger-color);
    margin-left: 5px;
    cursor: pointer;
    padding: 0;
    font-size: 1.1em;
}


#messageInput {
    flex-grow: 1;
    padding-top: 8px; 
    padding-right: 12px;
    padding-bottom: 10px;
    padding-left: 12px;
    border: 1px solid var(--border-color);
    border-radius: 20px; 
    background-color: var(--input-bg);
    color: var(--primary-text);
    font-size: 1em;
    resize: none; 
    margin-right: 10px;
    max-height: 150px; 
    overflow-y: auto;
    line-height: 1.4;
}
#messageInput:focus {
    outline: none;
    border-color: var(--user-bubble-bg);
    box-shadow: 0 0 0 2px rgba(61, 90, 128, 0.3);
}

/* Markdown specific styling for chat messages */
.message-item .md-content p { margin: 0 0 0.5em 0; }
.message-item .md-content h1, .message-item .md-content h2, .message-item .md-content h3 { margin-top: 0.8em; margin-bottom: 0.4em; color: var(--highlight-text); }
.message-item .md-content ul, .message-item .md-content ol { margin-left: 20px; padding-left: 0; }
.message-item .md-content blockquote { border-left: 3px solid var(--user-bubble-bg); margin-left: 0; padding-left: 1em; color: var(--secondary-text); }
.message-item .md-content code { background-color: rgba(0,0,0,0.2); padding: 0.2em 0.4em; border-radius: 3px; font-size: 0.9em; }
.message-item .md-content pre code { padding: 0; background: none; } 

.message-edit-textarea {
    /* width: 500px;  No longer fixed width */
    min-width: 250px; 
    min-height: 50px;
    padding: 8px;
    margin-top: 5px;
    margin-bottom: 5px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--input-bg);
    color: var(--primary-text);
    font-family: inherit;
    font-size: 0.95em;
    resize: both; 
    max-width: 100%; 
    box-sizing: border-box;
    /* Will be a flex child of .message-item.message-item-editing */
    flex-grow: 1;
}

.message-edit-controls {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 5px;
    /* Will be a flex child or sibling of textarea */
    width: 100%; /* if it's a block below textarea */
}
.message-edit-controls button {
    padding: 5px 10px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 0.9em;
    background-color: var(--button-bg);
    color: var(--primary-text); 
    transition: background-color 0.2s ease;
}
.message-edit-controls button:hover {
    background-color: var(--button-hover-bg);
}
body.light-theme .message-edit-controls button {
    color: #ffffff; 
}

/* Editing mode for QQ Style */
.message-item.message-item-editing {
    flex-direction: column; /* Stack textarea and controls vertically */
    align-items: stretch; /* Make children (textarea, controls) take full width */
}
.message-item.message-item-editing .chat-avatar,
.message-item.message-item-editing .name-time-block {
    display: none; /* Hide avatar and name/time block when editing */
}
.message-item.message-item-editing .md-content {
    display: none; /* Hide the original bubble content */
}

/* Ensure the details-and-bubble-wrapper is also hidden when editing, if it exists */
.message-item.message-item-editing .details-and-bubble-wrapper {
    display: none;
}

@media (max-width: 768px) {
    .message-item .md-content {
        max-width: 85%; /* Adjust bubble width for smaller screens */
    }
}

.attachment-preview-area {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    width: 100%;
    order: -1; /* To appear above the input field */
    margin-bottom: 8px;
    padding: 0; /* Keep padding at 0, as items themselves have padding */
}

.attachment-preview-item {
    background-color: var(--accent-bg);
    color: var(--primary-text);
    border-radius: 8px; /* Rounded rectangle, not a pill */
    padding: 6px 10px; /* Adjusted padding */
    display: flex;
    align-items: center;
    font-size: 0.9em;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.2s ease-in-out;
    cursor: default;
    /* position: relative; -- only if remove button is absolute */
    max-width: 200px; /* Prevent items from becoming too wide */
}

.attachment-preview-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}

.attachment-thumbnail-image {
    width: 32px; /* Thumbnail size */
    height: 32px;
    object-fit: cover;
    border-radius: 4px; /* Slightly rounded corners for the image */
    margin-right: 8px;
    border: 1px solid var(--border-color); /* Optional border for the image */
}

.file-preview-icon {
    margin-right: 8px;
    font-size: 1.2em; /* Slightly larger icon for non-images */
    width: 32px; /* Match thumbnail width for alignment */
    height: 32px; /* Match thumbnail height */
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-preview-name {
    display: inline; /* Make filename visible */
    flex-grow: 1; /* Allow name to take available space */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 8px; /* Space before remove button */
    line-height: 1.2; /* Ensure text is vertically centered well */
}

.file-preview-remove-btn {
    /* Removed absolute positioning, now part of flex flow */
    background: transparent;
    border: none;
    color: var(--secondary-text); /* Muted color */
    padding: 0; /* Reset padding */
    margin-left: auto; /* Pushes button to the right within the flex container */
    cursor: pointer;
    font-size: 1.3em; /* Larger 'x' */
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s ease;
    align-self: center; /* Vertically align with other items */
    width: 20px; /* Give some clickable area */
    height: 20px;
}

.file-preview-remove-btn:hover {
    color: var(--danger-color); /* Highlight on hover */
}

.message-attachment-image-thumbnail {
    max-width: 150px;
    max-height: 150px;
    border-radius: 8px;
    cursor: pointer;
    object-fit: cover;
    margin: 5px;
    border: 2px solid var(--button-bg);
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.message-attachment-image-thumbnail:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.message-item .md-content .quoted-text {
    color: var(--quoted-text);
}

/* @tag 高亮样式 */
.highlighted-tag {
    color: var(--highlight-text); /* 使用主题的高亮颜色 */
    background-color: rgba(var(--rgb-secondary-bg-dark, 40, 40, 44), 0.2); /* 轻微背景，暗色主题兼容 */
    padding: 0.1em 0.3em;
    border-radius: 4px;
    font-weight: 500;
}

body.light-theme .highlighted-tag {
    color: var(--highlight-text-light);
    background-color: rgba(var(--rgb-secondary-bg-light, 255, 255, 255), 0.3); /* 轻微背景，亮色主题兼容 */
}

/* --- @Note Suggestion Popup --- */
#note-suggestion-popup {
    position: fixed;
    background-color: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
    width: 300px; /* Or adjust as needed */
    display: none; /* Initially hidden */
}

.suggestion-item {
    padding: 8px 12px;
    color: var(--primary-text);
    cursor: pointer;
    /* Default (dark theme) subtle separator */
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

body.light-theme .suggestion-item {
    /* Light theme subtle separator */
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item:hover,
.suggestion-item.active {
    background-color: var(--accent-bg);
    color: var(--highlight-text);
}
/* --- Light Theme Code Block Styles --- */
body.light-theme .message-item .md-content pre {
    background-color: rgba(255, 250, 240, 0.85); /* 米白磨砂背景 */
    color: #333333; /* 深灰色基础文字 */
    border-color: rgba(0, 0, 0, 0.1);
}

/* --- Light Theme Syntax Highlighting Overrides --- */
/* Default code color */
body.light-theme .message-item .md-content pre code {
    color: #333333;
}

/* Keywords (e.g., def, if, return) */
body.light-theme .message-item .md-content pre .hljs-keyword {
    color: #d73a49; /* Dark Red */
}

/* Built-ins, types (e.g., print, True) */
body.light-theme .message-item .md-content pre .hljs-built_in {
    color: #6f42c1; /* Dark Purple */
}

/* Strings */
body.light-theme .message-item .md-content pre .hljs-string {
    color: #032f62; /* Dark Blue */
}

/* Comments */
body.light-theme .message-item .md-content pre .hljs-comment {
    color: #6a737d; /* Grey */
    font-style: italic;
}

/* Numbers */
body.light-theme .message-item .md-content pre .hljs-number {
    color: #005cc5; /* Blue */
}

/* Function/Class names */
body.light-theme .message-item .md-content pre .hljs-title,
body.light-theme .message-item .md-content pre .hljs-class .hljs-title {
    color: #6f42c1; /* Dark Purple */
}

/* Function parameters */
body.light-theme .message-item .md-content pre .hljs-params {
    color: #24292e; /* Almost black */
}

/* Python decorators, annotations */
body.light-theme .message-item .md-content pre .hljs-meta {
    color: #e36209; /* Dark Orange */
}
/*
 * Final, more robust fix for centering images.
 * Force the paragraph to be a flex container and center its content.
 * This should override any conflicting styles.
*/
.message-item .md-content p[style*="text-align: center"] {
    display: flex;
    justify-content: center;
    width: 100%; /* Ensure the paragraph takes full width to center content */
}

/*
 * Hotfix for AI-generated custom HTML blocks.
 * When the AI outputs a DIV with its own inline `max-width`, it can break
 * the responsive layout of our message bubble. This rule forces such DIVs
 * to be contained within the parent bubble, preventing layout conflicts
 * and weird text wrapping issues.
*/
.message-item .md-content > div[style] {
    max-width: 100% !important;
    width: auto !important;
    box-sizing: border-box;
}

/*
 * Final fix for centering images.
 * When an image is inside a paragraph with `text-align: center`,
 * we turn the paragraph into a flex container to reliably center the image.
*/