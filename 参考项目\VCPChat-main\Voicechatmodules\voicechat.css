/* Voicechatmodules/voicechat.css */

body, html {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--primary-bg);
    color: var(--primary-text);
    display: flex;
    flex-direction: column;
}


.chat-header {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: var(--secondary-bg);
    border-bottom: 1px solid var(--border-color);
    flex-shrink: 0;
    -webkit-app-region: drag; /* Make the header draggable */
    user-select: none; /* Prevent text selection while dragging */
}

/* Make sure controls in the header are clickable */
.chat-header button {
    -webkit-app-region: no-drag;
}

.chat-header .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
}

.chat-header h3 {
    margin: 0;
    font-size: 1.1em;
    font-weight: 600;
    flex-grow: 1;
}

.header-controls {
    display: flex;
}

.window-control-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.window-control-button svg {
    width: 12px;
    height: 12px;
    fill: var(--primary-text);
}

.window-control-button:hover {
    background-color: var(--button-hover-bg);
}

.chat-messages-container {
    flex-grow: 1;
    overflow-y: auto;
    padding: 10px;
}

.chat-messages {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.chat-input-area {
    display: flex;
    padding: 10px;
    border-top: 1px solid var(--border-color);
    background-color: var(--secondary-bg);
    align-items: flex-end;
}

#messageInput {
    flex-grow: 1;
    resize: none;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    padding: 8px 12px;
    font-size: 1em;
    background-color: var(--input-bg);
    color: var(--primary-text);
    max-height: 150px;
    overflow-y: auto;
}

#sendMessageBtn, #toggleInputModeBtn {
    background-color: var(--button-bg);
    border: none;
    border-radius: 6px;
    padding: 8px;
    margin-left: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

#sendMessageBtn:hover, #toggleInputModeBtn:hover {
    background-color: var(--button-hover-bg);
}

#sendMessageBtn svg, #toggleInputModeBtn svg {
    fill: var(--primary-text);
}