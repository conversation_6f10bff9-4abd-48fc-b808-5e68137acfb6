/*
 * styles/themes/themes卡提西亚.css
 * Curated by <PERSON><PERSON> for Professor <PERSON>
 * New Palette Set: "Galactic Maiden" and "Fallen Knight"
 * A theme of duality, capturing both serene divinity and shattered power.
 */

/* * =================================================================
 * Dark Theme: Fallen Knight Palette (挥剑芙露德利斯)
 * Inspired by a knight's resolve amidst a shattered, dark world.
 * A powerful, sharp, and high-contrast theme.
 * =================================================================
 */
:root {
    /* --- Wallpaper Interface --- */
    /*
     * 壁纸是主题的灵魂！
     * 使用方法: 确认图片路径正确, 建议将图片放入 ../assets/ 文件夹
     * 例如: url('../assets/cartesia-dark.png')
     */
    --chat-wallpaper-dark: url('../assets/wallpaper/fleurdelys.png');

    /* --- Base Colors --- */
    --primary-bg: #0a0f14;      /* 主背景 - 深邃近黑的夜空 */
    --secondary-bg: #141a22;    /* 侧边栏/面板背景 - 稍亮的暗蓝 */
    --tertiary-bg: #05070a;     /* 聊天区背景 - 更纯粹的黑 */
    --accent-bg: #2a5a75;       /* 悬停/选中背景 - 柔和的电光蓝 */
    --border-color: #33c1ff;    /* 边框颜色 - 明亮的电光蓝 */
    --input-bg: #141a22;        /* 输入框背景 */

    /* --- Frosted Panel Background --- */
    --panel-bg-dark: rgba(20, 26, 34, 0.8); /* 基于--secondary-bg，增加80%不透明度 */

    /* --- Text Colors --- */
    --primary-text: #eaeef9;    /* 主要文字 - 柔和的银白色 */
    --secondary-text: #c8d1df;  /* 次要/标题文字 - 中性灰蓝 */
    --highlight-text: #33c1ff;  /* 高亮文字 - 核心电光蓝 */
    --text-on-accent: #ffffff;  /* 在强调色背景上的文字 */
    --placeholder-text: #6a7889; /* 输入框占位文字 */
    --quoted-text: #33c1ff;     /* 引用文本颜色 */
    --user-text: #e0f2f7;       /* 用户气泡文字 - 很浅的淡蓝色 */
    --agent-text: #eaeef9;      /* Agent气泡文字 - 柔和的银白色 */

    /* --- Bubble Colors (Frosted Glass Ready) --- */
    --user-bubble-bg: rgba(51, 193, 255, 0.15); /* 用户气泡 - 半透明电光蓝 */
    --assistant-bubble-bg: rgba(168, 178, 192, 0.1); /* AI气泡 - 半透明灰蓝 */

    /* --- UI Element Colors --- */
    --button-bg: #33c1ff;           /* 按钮背景 - 电光蓝 */
    --button-hover-bg: #66d3ff;      /* 按钮悬停 */
    --danger-color: #e74c3c;        /* 危险操作 - 经典红 */
    --danger-hover-bg: #c0392b;
    --success-color: #00cec9;       /* 成功操作 - 薄荷青 */
    --notification-bg: #141a22;
    --notification-header-bg: #0a0f14;
    --notification-border: #33c1ff;
    --tool-bubble-bg: rgba(168, 178, 192, 0.15);
    --tool-bubble-border: #33c1ff;

    /* --- Scrollbar --- */
    --scrollbar-track: rgba(20, 26, 34, 0.5);
    --scrollbar-thumb: rgba(42, 90, 117, 0.7);
    --scrollbar-thumb-hover: rgba(51, 193, 255, 0.6);

    /* --- 流式输出动画光晕 Shimmer Effect for Loading --- */
    --shimmer-color-transparent: rgba(234, 239, 249, 0.1);
    --shimmer-color-highlight: rgba(234, 239, 249, 0.8);

    /* --- Text Shadow for Frosted Panels --- */
    --panel-text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);

body {
    background-image: var(--chat-wallpaper-dark);
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}
    /* --- Unified Panel Background --- */
    --panel-bg: var(--panel-bg-dark);
}

/* * =================================================================
 * Light Theme: Maiden in the Sun Palette (阳光少女)
 * Inspired by a gentle maiden resting in a sun-drenched garden.
 * A bright, fresh, and heartwarming theme.
 * =================================================================
 */
body.light-theme {
    /* Wallpaper and background properties */
    background-image: var(--chat-wallpaper-light);
    background-size: cover;
    background-position: center;
    background-attachment: fixed;

    /* --- Wallpaper Interface --- */
    /* 请将 'cartesia-garden.png' 替换为您新图片的文件名 */
    --chat-wallpaper-light: url('../assets/wallpaper/cartethyia.png');

    /* --- Base Colors --- */
    --primary-bg: #fdfaf2;      /* 主背景 - 阳光暖白 */
    --secondary-bg: #f5f8f0;    /* 侧边栏 - 嫩芽白 */
    --tertiary-bg: #ffffff;     /* 聊天区 - 纯净白 */
    --accent-bg: #e0e8f5;       /* 悬停背景 - 浅天蓝 */
    --border-color: #cce0d0;    /* 边框 - 薄荷绿 */
    --input-bg: #ffffff;        /* 输入框 */

    /* --- Frosted Panel Background --- */
    --panel-bg-light: rgba(245, 248, 240, 0.85); /* 基于--secondary-bg，增加85%不透明度 */

    /* --- Text Colors --- */
    --primary-text: #4a443a;    /* 主要文字 - 深茶色 */
    --secondary-text: #8a7e6d;  /* 次要文字 - 暖灰褐 */
    --highlight-text: #00aaff;  /* 高亮文字 - 圣泉蓝 */
    --text-on-accent: #4a443a;  /* 强调背景文字 */
    --placeholder-text: #b0a89a; /* 占位文字 */
    --quoted-text: #00aaff;     /* 引用文字 - 圣泉蓝 */
    --user-text: #4a443a;       /* 用户气泡文字 - 深茶色 */
    --agent-text: #4a443a;      /* Agent气泡文字 - 深茶色 */

    /* --- Bubble Colors --- */
    --user-bubble-bg: rgba(0, 170, 255, 0.1);     /* 用户气泡 - 半透明圣泉蓝 */
    --assistant-bubble-bg: rgba(74, 68, 58, 0.05); /* AI气泡 - 半透明深茶色 */

    /* --- UI Element Colors --- */
    --button-bg: #00aaff;           /* 按钮 - 圣泉蓝 */
    --button-hover-bg: #33bbff;     /* 按钮悬停 */
    --danger-color: #e57373;        /* 危险色 - 温和红 */
    --danger-hover-bg: #ef5350;
    --success-color: #81c784;       /* 成功色 - 柔和绿 */
    --notification-bg: #f5f8f0;
    --notification-header-bg: #fdfaf2;
    --notification-border: #00aaff;
    --tool-bubble-bg: rgba(129, 199, 132, 0.1);
    --tool-bubble-border: #81c784;

    /* --- Scrollbar --- */
    --scrollbar-track: rgba(245, 248, 240, 0.7);
    --scrollbar-thumb: rgba(204, 224, 208, 0.8);
    --scrollbar-thumb-hover: rgba(0, 170, 255, 0.6);

    /* --- 流式输出动画光晕 Shimmer Effect --- */
    --shimmer-color-transparent: rgba(74, 68, 58, 0.04);
    --shimmer-color-highlight: rgba(74, 68, 58, 0.8);

    /* --- Unified Panel Background --- */
    --panel-bg: var(--panel-bg-light);
}

/* * =================================================================
 * Enhanced Frosted Glass Effect
 * 增强磨砂玻璃效果 - 小灵特制
 * =================================================================
 */

/* 为聊天气泡添加更强的磨砂玻璃效果 */
.message-bubble {
    backdrop-filter: blur(12px) saturate(120%);
    -webkit-backdrop-filter: blur(12px) saturate(120%);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬停时的微妙发光效果 */
.message-bubble:hover {
    backdrop-filter: blur(16px) saturate(130%);
    -webkit-backdrop-filter: blur(16px) saturate(130%);
    transform: translateY(-1px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 为工具调用气泡添加特殊效果 */
.tool-bubble {
    backdrop-filter: blur(10px) saturate(110%);
    -webkit-backdrop-filter: blur(10px) saturate(110%);
    border: 1px solid var(--tool-bubble-border);
    background: var(--tool-bubble-bg);
}

/* 深色主题下的特殊光效 */
:root .message-bubble {
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 浅色主题下的柔和阴影 */
body.light-theme .message-bubble {
    box-shadow: 
        0 4px 20px rgba(44, 62, 80, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.7);
}

