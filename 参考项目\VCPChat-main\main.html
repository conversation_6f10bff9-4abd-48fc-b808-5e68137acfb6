<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy"
          content="default-src 'self' data:;
                   script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com;
                   style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com;
                   img-src * data: file: blob:;
                   media-src * data: file:;
                   font-src 'self' https://cdn.jsdelivr.net;
                   connect-src * ws: wss:;">
    <title>VCPChat</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css" crossorigin="anonymous">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/contrib/auto-render.min.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script> <!-- 添加 marked.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
</head>
<body>
    <div class="title-bar">
        <div class="title-bar-text"><img src="assets/icon.png" alt="Logo" class="title-bar-logo"> VCPChat</div>
        <div class="title-bar-controls">
            <button id="settings-btn" class="title-bar-button" title="主题" onclick="window.electronAPI.openThemesWindow()">
                <svg viewBox="0 0 10 10">
                    <polygon points="5,8 0,2 10,2"></polygon>
                </svg>
            </button>
            <button id="minimize-btn" class="title-bar-button" title="最小化">
                <svg x="0px" y="0px" viewBox="0 0 10.2 1"><rect x="0" y="0" width="10.2" height="1"></rect></svg>
            </button>
            <button id="maximize-btn" class="title-bar-button" title="最大化">
                <svg viewBox="0 0 10 10"><path d="M0,0v10h10V0H0z M9,9H1V1h8V9z"></path></svg>
            </button>
            <button id="restore-btn" class="title-bar-button" title="还原" style="display: none;">
                <svg viewBox="0 0 10.2 10.1"><path d="M2.1,0v2H0v8.1h8.2v-2h2V0H2.1z M7.2,9.2H1.1V3h6.1V9.2z M9.2,7.1h-1V2H3.1V1h6.1V7.1z"></path></svg>
            </button>
            <button id="close-btn" class="title-bar-button close-button" title="关闭">
                <svg viewBox="0 0 10 10"><polygon points="10,1.01 8.99,0 5,3.99 1.01,0 0,1.01 3.99,5 0,8.99 1.01,10 5,6.01 8.99,10 10,8.99 6.01,5"></polygon></svg>
            </button>
        </div>
    </div>
    <div class="container">
        <aside class="sidebar">
            <div class="sidebar-tabs">
                <button class="sidebar-tab-button active" data-tab="agents">助手</button>
                <button class="sidebar-tab-button" data-tab="topics">话题</button>
                <button class="sidebar-tab-button" data-tab="settings">设置</button>
            </div>
            <div class="sidebar-tab-content active" id="tabContentAgents">
                <h2>VCP Agents</h2>
                <ul class="agent-list" id="agentList">
                    <!-- 示例:
                    <li class="active" data-agent-id="xiaoke">
                        <img src="path/to/xiaoke_avatar.png" alt="小克头像" class="avatar">
                        <span class="agent-name">猫娘小克</span>
                    </li>
                    -->
                </ul>
                <div class="sidebar-actions">
                    <button id="createNewAgentBtn" class="sidebar-button create-agent-btn small-button">创建Agent</button>
                    <button id="createNewGroupBtn" class="sidebar-button create-group-btn small-button">创建 Group</button>
                </div>
            </div>
            <div class="sidebar-tab-content" id="tabContentTopics">
                <!-- 话题内容区 -->
                <div class="topics-header">
                    <h2>话题</h2>
                    <div class="topic-search-container">
                        <input type="text" id="topicSearchInput" placeholder="搜索话题..." class="topic-search-input">
                        <!-- Search button removed -->
                    </div>
                </div>
                <ul class="topic-list" id="topicList">
                    <!-- 话题列表将在这里动态加载 -->
                </ul>
            </div>
            <div class="sidebar-tab-content" id="tabContentSettings">
                <!-- 设置内容区 -->
                <div class="settings-header-bar">
                    <h2>设置</h2>
                    <button id="globalSettingsBtn" class="sidebar-button global-settings-btn">全局设置</button>
                </div>
                
                <div id="agentSettingsContainer" style="display: none;"> <!-- Initially hidden -->
                    <h3 id="agentSettingsContainerTitle">助手设置: <span id="selectedAgentNameForSettings"></span></h3>
                    <form id="agentSettingsForm">
                        <input type="hidden" id="editingAgentId" name="agentId">
                        <div>
                            <label for="agentNameInput">Agent 名称:</label>
                            <input type="text" id="agentNameInput" name="name" required>
                        </div>
                        <div>
                            <label for="agentAvatarInput">Agent 头像 (留空不更改):</label>
                            <input type="file" id="agentAvatarInput" name="avatar" accept="image/png, image/jpeg, image/gif">
                            <img id="agentAvatarPreview" src="#" alt="头像预览" style="max-width: 80px; max-height: 80px; display: none; margin-top: 5px;">
                        </div>
                        <div>
                            <label for="agentSystemPrompt">系统提示词 (可使用 `{{AgentName}}` 占位符):</label>
                            <textarea id="agentSystemPrompt" name="systemPrompt" rows="6"></textarea>
                        </div>
                        <div>
                            <label for="agentModel">模型名称:</label>
                            <div class="model-input-container">
                                <input type="text" id="agentModel" name="model" placeholder="例如 gemini-2.5-flash-preview-05-20">
                                <button type="button" id="openModelSelectBtn" class="small-button" title="选择模型">
                                    <svg data-slot="icon" fill="none" stroke-width="1.5" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 15L12 18.75 15.75 15m-7.5-6L12 5.25 15.75 9"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div>
                            <label for="agentTemperature">Temperature (0-1):</label>
                            <input type="number" id="agentTemperature" name="temperature" min="0" max="1" step="0.1">
                        </div>
                        <div>
                            <label for="agentContextTokenLimit">上下文Token上限:</label>
                            <input type="number" id="agentContextTokenLimit" name="contextTokenLimit" min="0" step="100">
                        </div>
                         <div>
                            <label for="agentMaxOutputTokens">最大输出Token上限:</label>
                            <input type="number" id="agentMaxOutputTokens" name="maxOutputTokens" min="0" step="50">
                        </div>
                        <div>
                        <div>
                            <label for="agentTopP">Top P (0-1):</label>
                            <input type="number" id="agentTopP" name="top_p" min="0" max="0.95" step="0.05">
                        </div>
                        <div>
                            <label for="agentTopK">Top K (0-64):</label>
                            <input type="number" id="agentTopK" name="top_k" min="0" max="64" step="1">
                        </div>
                        <div class="form-group-inline">
                            <label>输出模式:</label>
                            <label for="agentStreamOutputTrue" style="margin-right: 15px;">
                                <input type="radio" id="agentStreamOutputTrue" name="streamOutput" value="true" checked> 流式
                            </label>
                            <label for="agentStreamOutputFalse" style="margin-left: -10px;"> <!-- 调整负外边距以减小间距 -->
                                <input type="radio" id="agentStreamOutputFalse" name="streamOutput" value="false"> 非流式
                            </label>
                        </div>

                        <hr class="form-divider">
                        
                        <div class="form-section-title">语音设置 (Sovits TTS)</div>
                        
                        <div>
                            <label for="agentTtsVoicePrimary">主语言模型:</label>
                            <div class="model-input-container">
                                <select id="agentTtsVoicePrimary" name="ttsVoicePrimary">
                                    <option value="">不使用语音</option>
                                </select>
                                <button type="button" id="refreshTtsModelsBtn" class="small-button" title="刷新模型列表">
                                    <svg data-slot="icon" fill="none" stroke-width="1.5" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 11.667 0l3.181-3.183m-4.991-2.691v-2.985h-4.992v2.985h4.992Zm-4.993 0v4.992h4.992v-4.992h-4.992Z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div>
                            <label for="agentTtsRegexPrimary">主语言正则 (留空则匹配全部):</label>
                            <input type="text" id="agentTtsRegexPrimary" name="ttsRegexPrimary" placeholder="例如 [^\[\]]+">
                        </div>

                        <hr class="form-divider-dashed">

                        <div>
                            <label for="agentTtsVoiceSecondary">副语言模型:</label>
                            <div class="model-input-container">
                                <select id="agentTtsVoiceSecondary" name="ttsVoiceSecondary">
                                    <option value="">不使用</option>
                                </select>
                            </div>
                        </div>
                        <div>
                            <label for="agentTtsRegexSecondary">副语言正则:</label>
                            <input type="text" id="agentTtsRegexSecondary" name="ttsRegexSecondary" placeholder="例如 \[(.*?)\]">
                        </div>

                        <hr class="form-divider-dashed">

                        <div>
                            <label for="agentTtsSpeed">语速:</label>
                            <div class="slider-container">
                                <input type="range" id="agentTtsSpeed" name="ttsSpeed" min="0.5" max="2.0" step="0.1" value="1.0">
                                <span id="ttsSpeedValue">1.0</span>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit">保存Agent设置</button>
                            <button type="button" id="deleteAgentBtn" class="danger-button">删除此Agent</button>
                        </div>
                    </form>
                </div>
                <p id="selectAgentPromptForSettings" style="display: block;">请先在“助手”标签页选择一个Agent以查看或修改其设置。</p>
            </div>
        </aside>
        <div class="resizer" id="resizerLeft"></div>
        <main class="main-content">
            <header class="chat-header">
                <h3 id="currentChatAgentName">选择一个Agent开始聊天</h3>
                <div class="chat-actions">
                    <button id="toggleAssistantBtn" class="header-button" title="划词助手">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" width="18" height="18" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 11.667 0l3.181-3.183m-4.991-2.691v-2.985h-4.992v2.985h4.992Zm-4.993 0v4.992h4.992v-4.992h-4.992Z"></path>
                        </svg>
                    </button>
                    <button id="toggleNotificationsBtn" class="header-button" title="切换通知面板">
                        <svg data-slot="icon" fill="none" stroke-width="1.5" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="18" height="18">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"></path>
                        </svg>
                    </button>
                    <button id="themeToggleBtn" class="header-button" title="切换主题">
                        <svg id="sun-icon" class="theme-icon" fill="none" stroke-width="1.5" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"></path>
                        </svg>
                        <svg id="moon-icon" class="theme-icon" fill="none" stroke-width="1.5" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" style="display: none;">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"></path>
                        </svg>
                    </button>
                    <button id="currentAgentSettingsBtn" class="header-button" title="当前Agent设置" style="display: none;">⚙️ Agent设置</button>
                    <button id="voiceChatBtn" class="header-button" title="语音聊天" style="margin-right: 5px; display: none;">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="18" height="18">
                            <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.49 6-3.31 6-6.72h-1.7z"></path>
                        </svg>
                    </button>
                    <button id="voiceChatBtn" class="header-button" title="语音聊天" style="margin-right: 5px; display: none;">
                        <span style="vertical-align: middle; margin-left: 5px;">📞 语音聊天</span>
                    </button>
                </div>
            </header>
            <div class="chat-messages-container">
                <div class="chat-messages" id="chatMessages">
                    </div>
            </div>
            <footer class="chat-input-area">
                <div class="attachment-preview-area" id="attachmentPreviewArea"></div>
                <textarea id="messageInput" placeholder="输入消息... (Shift+Enter 换行)" rows="1" disabled></textarea>
                <button id="sendMessageBtn" title="发送消息 (Ctrl+Enter)" disabled>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="24" height="24"><path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/></svg>
                </button>
                 <button id="attachFileBtn" title="发送文件" disabled>
                    <svg data-slot="icon" fill="none" stroke-width="1.5" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="20" height="20">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"></path>
                    </svg>
                </button>
            </footer>
        </main>
        <div class="resizer" id="resizerRight"></div>
        <aside class="notifications-sidebar" id="notificationsSidebar">
            <header class="notifications-header">
                <h4 id="notificationTitle" style="display: none;">VCP 通知</h4>
                <div class="datetime-container">
                    <div id="digitalClock" class="digital-clock"></div>
                    <div id="dateDisplay" class="date-display"></div>
                </div>
                <div class="notification-header-actions">
                    <button id="openAdminPanelBtn" class="header-button" title="打开服务器管理面板">
                        <svg data-slot="icon" fill="none" stroke-width="1.5" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="18" height="18">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076-.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"></path>
                        </svg>
                    </button>
                    <button id="clearNotificationsBtn" title="清空通知">清空</button>
                </div>
            </header>
            <div class="notifications-status" id="vcpLogConnectionStatus">
                VCPLog: 未连接
            </div>
            <ul class="notifications-list" id="notificationsList">
                </ul>
            <div id="inviteAgentButtonsContainer" class="invite-agent-buttons-container" style="display: none; padding: 10px; border-top: 1px solid var(--border-color); margin-top:10px;">
                <!-- 邀请发言按钮将由 JavaScript 动态填充到这里 -->
            </div>
            <hr class="section-divider">
            <div class="notes-section">
                <button id="openTranslatorBtn" class="header-button" title="翻译">
                    <svg data-slot="icon" fill="none" stroke-width="1.5" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="18" height="18">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 21h7.5m-9-13.5h6m-6 4.5h6m-10.5-6.75h6a2.25 2.25 0 0 1 2.25 2.25v10.5a2.25 2.25 0 0 1-2.25 2.25h-6a2.25 2.25 0 0 1-2.25-2.25v-10.5A2.25 2.25 0 0 1 10.5 3.75ZM15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z" />
                    </svg>
                    翻译
                </button>
                <button id="openNotesBtn" class="header-button" title="打开笔记">
                    <svg data-slot="icon" fill="none" stroke-width="1.5" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="18" height="18">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.2-8.2zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"></path>
                    </svg>
                    笔记
                </button>
                <button id="openMusicBtn" class="header-button" title="音乐播放器">
                    <svg data-slot="icon" fill="none" stroke-width="1.5" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="18" height="18">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 18V5l12-2v13M9 18a3 3 0 100-6 3 3 0 000 6zm12-2a3 3 0 100-6 3 3 0 000 6z"></path>
                    </svg>
                    音乐
                </button>
                <button id="openDiceBtn" class="header-button" title="超级骰子">
                    <svg data-slot="icon" fill="none" stroke-width="1.5" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="18" height="18">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 9.563V9a3 3 0 0 1 6 0v.563m-6 0a3 3 0 0 0 6 0m-6 0h6m-3 6.375a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"></path>
                    </svg>
                    骰子
                </button>
            </div>
        </aside>
    </div>
 
    <div class="modal" id="globalSettingsModal">
        <div class="modal-content">
            <span class="close-button" onclick="uiHelperFunctions.closeModal('globalSettingsModal')">×</span>
            <h2>全局设置</h2>
            <form id="globalSettingsForm">
                <div>
                    <label for="userName">用户名:</label>
                    <input type="text" id="userName" name="userName" placeholder="您的用户名" required>
                </div>
                <div class="form-group-inline-avatar"> <!-- New block for user avatar -->
                    <label for="userAvatarInput">添加用户头像:</label>
                    <input type="file" id="userAvatarInput" name="userAvatar" accept="image/png, image/jpeg, image/gif">
                    <img id="userAvatarPreview" src="#" alt="用户头像预览" style="max-width: 60px; max-height: 60px; display: none; margin-top: 5px; border-radius: 50%;">
                </div>
                <div>
                    <label for="vcpServerUrl">VCP 服务器 URL:</label>
                    <input type="url" id="vcpServerUrl" name="vcpServerUrl" placeholder="需手动添加v1/chat/completions" required>
                </div>
                <div>
                    <label for="vcpApiKey">VCP API Key:</label>
                    <input type="password" id="vcpApiKey" name="vcpApiKey">
                </div>
                <div>
                    <label for="vcpLogUrl">VCP WebSocket服务器 URL:</label>
                    <input type="url" id="vcpLogUrl" name="vcpLogUrl">
                </div>
                <div>
                    <label for="vcpLogKey">VCP WebSocket鉴权 Key:</label>
                    <input type="text" id="vcpLogKey" name="vcpLogKey">
                </div>
                <div class="form-group">
                    <label>网络笔记路径:</label>
                    <div id="networkNotesPathsContainer" style="display: flex; flex-direction: column; gap: 8px;">
                        <!-- Path inputs will be dynamically added here -->
                    </div>
                    <button type="button" id="addNetworkPathBtn" class="sidebar-button small-button" style="margin-top: 8px; width: auto; padding: 4px 10px;">添加路径</button>
                </div>
                <div class="form-group-inline" style="justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <label for="enableAgentBubbleTheme">开启Agent自定义气泡主题</label>
                    <label class="switch">
                        <input type="checkbox" id="enableAgentBubbleTheme" name="enableAgentBubbleTheme">
                        <span class="slider round"></span>
                    </label>
                </div>
                <div class="form-group-inline" style="justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <label for="enableSmoothStreaming">开启高级流式渲染</label>
                    <label class="switch">
                        <input type="checkbox" id="enableSmoothStreaming" name="enableSmoothStreaming">
                        <span class="slider round"></span>
                    </label>
                </div>
                <div class="form-group-inline">
                    <div>
                        <label for="minChunkBufferSize" style="display: block; margin-bottom: 4px;">最小渲染Chunk字数 (≥1):</label>
                        <input type="number" id="minChunkBufferSize" name="minChunkBufferSize" min="1" value="1" style="width: 80px;">
                    </div>
                    <div style="margin-left: 20px;">
                        <label for="smoothStreamIntervalMs" style="display: block; margin-bottom: 4px;">最小渲染Chunk间隔 (ms, ≥1):</label>
                        <input type="number" id="smoothStreamIntervalMs" name="smoothStreamIntervalMs" min="1" value="25" style="width: 80px;">
                    </div>
                </div>
                <div>
                </div>

                <hr style="border: none; border-top: 1px solid var(--border-color); margin: 20px 0;">

                <div id="assistantAgentContainer" style="margin-top: 15px;">
                    <label for="assistantAgent">划词助手 Agent:</label>
                    <select id="assistantAgent" name="assistantAgent">
                        <option value="">请选择一个Agent</option>
                        <!-- Agent list will be populated by JS -->
                    </select>
                </div>

                <hr style="border: none; border-top: 1px solid var(--border-color); margin: 20px 0;">

                <div class="form-group-inline" style="justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <label for="enableDistributedServer">启用VCP分布式服务器</label>
                    <label class="switch">
                        <input type="checkbox" id="enableDistributedServer" name="enableDistributedServer">
                        <span class="slider round"></span>
                    </label>
                </div>


                <div class="form-group-inline" style="justify-content: space-between; align-items: center;">
                    <label for="agentMusicControl">启用Agent音乐控制</label>
                    <label class="switch">
                        <input type="checkbox" id="agentMusicControl" name="agentMusicControl">
                        <span class="slider round"></span>
                    </label>
                </div>

                <button type="submit" style="margin-top: 20px;">保存全局设置</button>
            </form>
        </div>
    </div>

    <div class="modal" id="avatarCropperModal">
        <div class="modal-content" style="max-width: 420px;"> <!-- Adjusted width for cropper -->
            <span class="close-button" onclick="uiHelperFunctions.closeModal('avatarCropperModal'); document.getElementById('agentAvatarInput').value = ''; if(document.getElementById('userAvatarInput')) document.getElementById('userAvatarInput').value = '';">&times;</span>
            <h2>裁剪头像</h2>
            <div id="avatarCropperContainer" style="width: 360px; height: 360px; margin: 10px auto; position: relative; border: 1px solid var(--border-color); overflow: hidden; background-color: #ccc;">
                <!-- Canvas for the image -->
                <canvas id="avatarCanvas" width="360" height="360"></canvas>
                <!-- Div for the circular overlay -->
                <div id="avatarCropOverlay" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; cursor: grab;">
                    <svg width="100%" height="100%" viewBox="0 0 360 360" style="pointer-events: none;"> <!-- SVG itself non-interactive -->
                        <defs>
                            <mask id="circleMask">
                                <rect width="360" height="360" fill="white"/>
                                <circle id="cropCircle" cx="180" cy="180" r="100" fill="black"/>
                            </mask>
                        </defs>
                        <rect width="360" height="360" fill="rgba(0,0,0,0.5)" mask="url(#circleMask)"/>
                        <circle id="cropCircleBorder" cx="180" cy="180" r="100" fill="none" stroke="rgba(255,255,255,0.7)" stroke-width="2" stroke-dasharray="5,5"/>
                    </svg>
                </div>
            </div>
            <div style="text-align: center; margin-top: 0px; margin-bottom:15px; font-size: 0.9em; color: var(--secondary-text);"><small>使用鼠标滚轮缩放, 拖动选区</small></div>
            <div class="form-actions" style="margin-top: 10px;">
                <button type="button" id="cancelCropBtn" class="sidebar-button" style="background-color: var(--button-bg); color: var(--primary-text);">取消</button>
                <button type="button" id="confirmCropBtn" class="sidebar-button" style="background-color: var(--user-bubble-bg); color: white;">确认裁剪</button>
            </div>
        </div>
    </div>

<div class="modal" id="createGroupModal">
        <div class="modal-content">
            <span class="close-button" onclick="uiHelperFunctions.closeModal('createGroupModal')">×</span>
            <h2>创建新群组</h2>
            <form id="createGroupForm">
                <div>
                    <label for="newGroupNameInput">群组名称:</label>
                    <input type="text" id="newGroupNameInput" name="groupName" placeholder="请输入群组名称" required>
                </div>
                <div class="form-actions">
                    <button type="button" class="button-secondary" onclick="uiHelperFunctions.closeModal('createGroupModal')">取消</button>
                    <button type="submit" class="button-primary">创建</button>
                </div>
            </form>
        </div>
    </div>
    <!-- Agent Settings Modal is now removed, its content moved to #tabContentSettings -->

    <script src="modules/topicSummarizer.js"></script>
    <script src="modules/notificationRenderer.js"></script>
    <script type="module" src="modules/messageRenderer.js"></script>
    <script src="modules/inputEnhancer.js"></script>
    <script src="modules/itemListManager.js"></script>
    <script src="modules/topicListManager.js"></script>
    <script src="modules/chatManager.js"></script>
    <script src="modules/uiManager.js"></script>
    <script src="modules/settingsManager.js"></script>
    <script src="modules/ui-helpers.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
    <script src="Groupmodules/grouprenderer.js"></script>
    <script type="module" src="modules/renderer/colorUtils.js"></script>
    <script type="module" src="modules/renderer/imageHandler.js"></script>
    <script type="module" src="modules/renderer/domBuilder.js"></script>
    <script type="module" src="modules/renderer/streamManager.js"></script>
    <script type="module" src="modules/renderer/contentProcessor.js"></script>
    <script type="module" src="modules/renderer/messageContextMenu.js"></script>
    <script type="module" src="renderer.js" defer></script>

    <!-- Model Selection Modal -->
    <div class="modal" id="modelSelectModal">
        <div class="modal-content">
            <span class="close-button" onclick="uiHelperFunctions.closeModal('modelSelectModal')">×</span>
            <h2>选择模型</h2>
            <div class="model-search-container">
                <input type="text" id="modelSearchInput" placeholder="搜索模型..." class="model-search-input">
                <button type="button" id="refreshModelsBtn" class="small-button" title="刷新模型列表">
                    <svg data-slot="icon" fill="none" stroke-width="1.5" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" width="16" height="16">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 11.667 0l3.181-3.183m-4.991-2.691v-2.985h-4.992v2.985h4.992Zm-4.993 0v4.992h4.992v-4.992h-4.992Z"></path>
                    </svg>
                </button>
            </div>
            <ul id="modelList" class="model-list">
                <!-- Models will be dynamically loaded here -->
            </ul>
        </div>
    </div>

    <!-- Global Search Modal -->
    <div id="global-search-modal" class="search-modal-overlay" style="display: none;">
        <div class="search-modal-content">
            <div class="search-modal-header">
                <h3>全局搜索</h3>
                <button id="global-search-close-button" class="search-modal-close-button">&times;</button>
            </div>
            <div class="search-modal-body">
                <input type="text" id="global-search-input" placeholder="搜索所有聊天记录 (Ctrl+F)...">
                <div id="global-search-results"></div>
                <div id="global-search-pagination"></div>
            </div>
        </div>
    </div>

    <div id="floating-toast-notifications-container"></div>
</body>
</html>